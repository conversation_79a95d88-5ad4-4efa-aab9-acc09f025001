import { useTheme } from '../contexts/themeContext';

export function DashboardCard({ title, value, trend, icon: Icon, color, themeColor }) {
  const { theme } = useTheme();

  // Get theme-aware colors
  const getCardColors = () => {
    if (themeColor && theme === 'parkwiz') {
      switch(themeColor) {
        case 'primary':
          return 'bg-gradient-to-r from-yellow-400 to-yellow-500';
        case 'secondary':
          return 'bg-gradient-to-r from-gray-500 to-gray-600';
        case 'success':
          return 'bg-gradient-to-r from-green-400 to-green-500';
        case 'info':
          return 'bg-gradient-to-r from-blue-400 to-blue-500';
        default:
          return color || 'bg-gradient-to-r from-yellow-400 to-yellow-500';
      }
    }
    return color || 'bg-blue-500';
  };

  return (
    <div
      className="rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border group"
      style={{
        backgroundColor: 'var(--color-bg-card)',
        borderColor: 'var(--color-border)',
        borderLeft: theme === 'parkwiz' ? '4px solid var(--color-accent)' : 'none'
      }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 pr-4">
          <p
            className="text-sm mb-3 font-medium uppercase tracking-wide"
            style={{ color: 'var(--color-text-secondary)' }}
          >
            {title}
          </p>
          <h3
            className="text-3xl lg:text-4xl font-bold mb-3 group-hover:scale-105 transition-transform duration-200"
            style={{ color: 'var(--color-text-primary)' }}
          >
            {value}
          </h3>
          {trend !== undefined && (
            <div className="flex items-center gap-2">
              <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-semibold ${
                trend >= 0
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
              }`}>
                <span>{trend >= 0 ? '↗' : '↘'}</span>
                <span>{Math.abs(trend)}%</span>
              </div>
              <span
                className="text-xs"
                style={{ color: 'var(--color-text-muted)' }}
              >
                vs last period
              </span>
            </div>
          )}
        </div>
        <div className={`p-4 rounded-xl ${getCardColors()} shadow-md group-hover:scale-110 transition-transform duration-200`}>
          <Icon className="w-8 h-8 text-white" />
        </div>
      </div>
    </div>
  );
}
