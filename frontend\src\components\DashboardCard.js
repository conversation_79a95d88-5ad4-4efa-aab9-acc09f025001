import { useTheme } from '../contexts/themeContext';

export function DashboardCard({ title, value, trend, icon: Icon, color, themeColor }) {
  const { theme } = useTheme();

  // Get theme-aware colors
  const getCardColors = () => {
    if (themeColor && theme === 'parkwiz') {
      switch(themeColor) {
        case 'primary':
          return 'bg-gradient-to-r from-yellow-400 to-yellow-500';
        case 'secondary':
          return 'bg-gradient-to-r from-gray-500 to-gray-600';
        case 'success':
          return 'bg-gradient-to-r from-green-400 to-green-500';
        case 'info':
          return 'bg-gradient-to-r from-blue-400 to-blue-500';
        default:
          return color || 'bg-gradient-to-r from-yellow-400 to-yellow-500';
      }
    }
    return color || 'bg-blue-500';
  };

  return (
    <div
      className="rounded-lg shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
      style={{
        backgroundColor: 'var(--color-bg-card)',
        borderLeft: theme === 'parkwiz' ? '4px solid var(--color-accent)' : 'none'
      }}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p
            className="text-sm mb-2 font-medium"
            style={{ color: 'var(--color-text-secondary)' }}
          >
            {title}
          </p>
          <h3
            className="text-3xl font-bold mb-2"
            style={{ color: 'var(--color-text-primary)' }}
          >
            {value}
          </h3>
          {trend !== undefined && (
            <div className="flex items-center">
              <span className={`text-sm font-semibold ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {trend >= 0 ? '↗' : '↘'} {Math.abs(trend)}%
              </span>
              <span
                className="text-xs ml-2"
                style={{ color: 'var(--color-text-muted)' }}
              >
                vs last period
              </span>
            </div>
          )}
        </div>
        <div className={`p-4 rounded-xl ${getCardColors()} shadow-md`}>
          <Icon className="w-8 h-8 text-white" />
        </div>
      </div>
    </div>
  );
}
