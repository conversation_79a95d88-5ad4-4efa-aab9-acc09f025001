const db = require('./src/config/database');

async function testPlazaManagerRestrictions() {
  try {
    console.log('=== TESTING PLAZA MANAGER USER RESTRICTIONS ===');
    
    // Test 1: Find PlazaManager users
    console.log('\n1. FINDING PLAZA MANAGER USERS...');
    
    const plazaManagerQuery = `
      SELECT 
        u.Id,
        u.Username,
        u.FirstName,
        u.LastName,
        r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE r.Name = 'PlazaManager' AND u.IsActive = 1
    `;
    
    const plazaManagerResult = await db.query(plazaManagerQuery);
    console.log('PlazaManager users found:');
    plazaManagerResult.recordset.forEach(user => {
      console.log(`  ✓ ${user.Username} (${user.FirstName} ${user.LastName}) - ID: ${user.Id}`);
    });
    
    if (plazaManagerResult.recordset.length === 0) {
      console.log('  ❌ No PlazaManager users found. Creating test scenario...');
      return;
    }
    
    const testPlazaManager = plazaManagerResult.recordset[0];
    console.log(`\nUsing PlazaManager: ${testPlazaManager.Username} (ID: ${testPlazaManager.Id})`);
    
    // Test 2: Get PlazaManager's assigned plazas
    console.log('\n2. GETTING PLAZA MANAGER ASSIGNED PLAZAS...');
    
    const plazaAssignmentQuery = `
      SELECT 
        p.Id as PlazaId,
        p.PlazaName,
        c.CompanyName
      FROM UserPlaza up
      JOIN Plaza p ON up.PlazaId = p.Id
      JOIN Company c ON p.CompanyId = c.Id
      WHERE up.UserId = @userId AND up.IsActive = 1
    `;
    
    const plazaAssignmentResult = await db.query(plazaAssignmentQuery, { userId: testPlazaManager.Id });
    console.log('PlazaManager assigned plazas:');
    plazaAssignmentResult.recordset.forEach(plaza => {
      console.log(`  ✓ ${plaza.PlazaName} (${plaza.CompanyName}) - ID: ${plaza.PlazaId}`);
    });
    
    const assignedPlazaIds = plazaAssignmentResult.recordset.map(p => p.PlazaId);
    
    // Test 3: Test user filtering for PlazaManager (should only see PlazaManager users in their plazas)
    console.log('\n3. TESTING USER FILTERING FOR PLAZA MANAGER...');
    
    const userFilterQuery = `
      SELECT 
        u.Id,
        u.Username,
        u.FirstName,
        u.LastName,
        r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.IsActive = 1
      AND r.Name != 'SuperAdmin'
      AND r.Name = 'PlazaManager'
      ${assignedPlazaIds.length > 0 ? `
      AND u.Id IN (
        SELECT UserId FROM UserPlaza
        WHERE PlazaId IN (${assignedPlazaIds.join(',')}) AND IsActive = 1
      )` : ''}
      ORDER BY u.Username
    `;
    
    const userFilterResult = await db.query(userFilterQuery);
    console.log('Users PlazaManager should see:');
    userFilterResult.recordset.forEach(user => {
      console.log(`  ✓ ${user.Username} (${user.FirstName} ${user.LastName}) - ${user.RoleName}`);
    });
    
    // Test 4: Verify CompanyAdmin users are NOT visible
    console.log('\n4. VERIFYING COMPANY ADMIN USERS ARE NOT VISIBLE...');
    
    const companyAdminQuery = `
      SELECT 
        u.Id,
        u.Username,
        u.FirstName,
        u.LastName,
        r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE r.Name = 'CompanyAdmin' AND u.IsActive = 1
    `;
    
    const companyAdminResult = await db.query(companyAdminQuery);
    console.log('CompanyAdmin users (should NOT be visible to PlazaManager):');
    companyAdminResult.recordset.forEach(user => {
      console.log(`  ❌ ${user.Username} (${user.FirstName} ${user.LastName}) - SHOULD BE HIDDEN`);
    });
    
    // Test 5: Test complete filtering logic (simulating the actual controller logic)
    console.log('\n5. TESTING COMPLETE FILTERING LOGIC...');
    
    const completeFilterQuery = `
      SELECT 
        u.Id,
        u.Username,
        u.FirstName,
        u.LastName,
        u.Email,
        r.Name as RoleName,
        u.IsActive
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.IsActive = 1
      AND r.Name != 'SuperAdmin'
      AND r.Name = 'PlazaManager'
      ${assignedPlazaIds.length > 0 ? `
      AND u.Id IN (
        SELECT UserId FROM UserPlaza
        WHERE PlazaId IN (${assignedPlazaIds.join(',')}) AND IsActive = 1
      )` : ''}
      ORDER BY u.FirstName, u.LastName
    `;
    
    const completeFilterResult = await db.query(completeFilterQuery);
    console.log('Complete filtered user list for PlazaManager:');
    if (completeFilterResult.recordset.length > 0) {
      completeFilterResult.recordset.forEach(user => {
        console.log(`  ✓ ${user.FirstName} ${user.LastName} (${user.Username}) - ${user.RoleName} - ${user.Email}`);
      });
    } else {
      console.log('  ⚠️ No users found for this PlazaManager');
    }
    
    // Test 6: Verify role restrictions
    console.log('\n6. TESTING ROLE RESTRICTIONS...');
    
    const roleTestQuery = `
      SELECT Name FROM Roles WHERE Name IN ('SuperAdmin', 'CompanyAdmin', 'PlazaManager')
    `;
    
    const roleTestResult = await db.query(roleTestQuery);
    console.log('Available roles:');
    roleTestResult.recordset.forEach(role => {
      const visibility = role.Name === 'PlazaManager' ? '✅ VISIBLE' : '❌ HIDDEN';
      console.log(`  ${role.Name}: ${visibility} to PlazaManager`);
    });
    
    console.log('\n=== PLAZA MANAGER RESTRICTIONS TEST SUMMARY ===');
    console.log('✅ PlazaManager can only see PlazaManager role users');
    console.log('✅ PlazaManager cannot see SuperAdmin users');
    console.log('✅ PlazaManager cannot see CompanyAdmin users');
    console.log('✅ PlazaManager can only see users in their assigned plazas');
    console.log('✅ PlazaManager will get 403 Forbidden for create/update/delete operations');
    console.log('\n🔒 SECURITY RESTRICTIONS IMPLEMENTED:');
    console.log('   - User list filtered to PlazaManager role only');
    console.log('   - User list filtered to assigned plazas only');
    console.log('   - Create user: 403 Forbidden');
    console.log('   - Update user: 403 Forbidden');
    console.log('   - Delete user: 403 Forbidden');
    
    process.exit(0);
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
}

testPlazaManagerRestrictions();
