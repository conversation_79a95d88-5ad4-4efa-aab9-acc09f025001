const fs = require('fs');
const path = require('path');

// List of management files to update
const managementFiles = [
  'frontend/src/pages/ManagePlaza.js',
  'frontend/src/pages/ManageLane.js',
  'frontend/src/pages/ManageDigitalPay.js',
  'frontend/src/pages/ManageFastag.js',
  'frontend/src/pages/ManageAnpr.js',
  'frontend/src/pages/ManageUHFReader.js',
  'frontend/src/pages/ManagePassRegistration.js',
  'frontend/src/pages/ManageCity.js',
  'frontend/src/pages/ManageAddress.js'
];

function updateToastImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace react-hot-toast import with useToast hook
    content = content.replace(
      /import toast from 'react-hot-toast';/g,
      "import { useToast } from '../hooks/useToast';"
    );
    
    // Add toast hook declaration after other hooks
    content = content.replace(
      /(const queryClient = useQueryClient\(\);)/,
      '$1\n  const toast = useToast();'
    );
    
    // Replace toast.success calls with showCrudSuccess
    content = content.replace(
      /toast\.success\('([^']+) created successfully'\)/g,
      "toast.showCrudSuccess('create', '$1')"
    );
    
    content = content.replace(
      /toast\.success\('([^']+) updated successfully'\)/g,
      "toast.showCrudSuccess('update', '$1')"
    );
    
    content = content.replace(
      /toast\.success\('([^']+) deleted successfully'\)/g,
      "toast.showCrudSuccess('delete', '$1')"
    );
    
    // Replace toast.error calls with showCrudError
    content = content.replace(
      /toast\.error\('Failed to create ([^']+)'\)/g,
      "toast.showCrudError('create', '$1', error.response?.data?.message)"
    );
    
    content = content.replace(
      /toast\.error\('Failed to update ([^']+)'\)/g,
      "toast.showCrudError('update', '$1', error.response?.data?.message)"
    );
    
    content = content.replace(
      /toast\.error\('Failed to delete ([^']+)'\)/g,
      "toast.showCrudError('delete', '$1', error.response?.data?.message)"
    );
    
    // Replace generic toast.error calls
    content = content.replace(
      /toast\.error\(/g,
      'toast.showError('
    );
    
    // Update onError handlers to include error parameter
    content = content.replace(
      /onError: \(\) => toast\.show/g,
      'onError: (error) => toast.show'
    );
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated ${filePath}`);
    
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
  }
}

// Update all management files
console.log('🔄 Updating toast integration in management files...\n');

managementFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    updateToastImports(filePath);
  } else {
    console.log(`⚠️  File not found: ${filePath}`);
  }
});

console.log('\n🎉 Toast integration update completed!');
console.log('\nNext steps:');
console.log('1. Test each management page for proper toast notifications');
console.log('2. Verify CRUD operations show appropriate success/error messages');
console.log('3. Check that all imports are working correctly');
