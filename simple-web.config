﻿<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
        <add value="default.htm" />
      </files>
    </defaultDocument>
    
    <directoryBrowse enabled="false" />
    
    <rewrite>
      <rules>
        <!-- Simple rule to serve the React app -->
        <rule name="ServeReactApp" stopProcessing="true">
          <match url="(.*)" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="frontend/build/index.html" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
