const db = require('./src/config/database');

async function testDashboardTableUpdate() {
  try {
    console.log('=== TESTING DASHBOARD TABLE UPDATE ===');
    
    // Test 1: Basic data availability
    console.log('\n1. TESTING DATA AVAILABILITY...');
    
    const dataCheckQuery = `
      SELECT 
        COUNT(*) as TotalRecords,
        MIN(ExitDateTime) as EarliestRecord,
        MAX(ExitDateTime) as LatestRecord,
        COUNT(DISTINCT PlazaName) as UniquePlazas,
        COUNT(DISTINCT VehicleNumber) as UniqueVehicles
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime IS NOT NULL
    `;
    
    const dataResult = await db.query(dataCheckQuery);
    const data = dataResult.recordset[0];
    
    console.log(`✓ Total records: ${data.TotalRecords}`);
    console.log(`✓ Date range: ${data.EarliestRecord} to ${data.LatestRecord}`);
    console.log(`✓ Unique plazas: ${data.UniquePlazas}`);
    console.log(`✓ Unique vehicles: ${data.UniqueVehicles}`);
    
    // Test 2: Dashboard summary query (similar to what's used in getSummaryData)
    console.log('\n2. TESTING DASHBOARD SUMMARY QUERY...');
    
    const summaryQuery = `
      SELECT
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
        COUNT(*) as TransactionCount,
        COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
      WHERE t.ExitDateTime >= DATEADD(DAY, -7, GETDATE())
      OPTION (OPTIMIZE FOR UNKNOWN)
    `;
    
    const summaryResult = await db.query(summaryQuery);
    const summary = summaryResult.recordset[0];
    
    console.log(`✓ Total Revenue (last 7 days): ₹${summary.TotalRevenue}`);
    console.log(`✓ Transaction Count: ${summary.TransactionCount}`);
    console.log(`✓ Vehicle Count: ${summary.VehicleCount}`);
    console.log(`✓ Average Duration: ${summary.AvgDuration} minutes`);
    
    // Test 3: Payment method breakdown
    console.log('\n3. TESTING PAYMENT METHOD QUERY...');
    
    const paymentQuery = `
      SELECT
        ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
        COUNT(*) as transactionCount
      FROM tblParkwiz_Parking_Data t
      WHERE t.ExitDateTime >= DATEADD(DAY, -7, GETDATE())
      GROUP BY t.PaymentMode
      ORDER BY totalRevenue DESC
    `;
    
    const paymentResult = await db.query(paymentQuery);
    console.log('Payment methods (last 7 days):');
    paymentResult.recordset.forEach(payment => {
      console.log(`  ✓ ${payment.paymentMode}: ₹${payment.totalRevenue} (${payment.transactionCount} transactions)`);
    });
    
    // Test 4: Recent transactions
    console.log('\n4. TESTING RECENT TRANSACTIONS QUERY...');
    
    const recentQuery = `
      SELECT TOP(5)
        t.PakringDataID,
        t.PlazaName,
        t.VehicleNumber,
        t.EntryDateTime,
        t.ExitDateTime,
        t.ParkingFee,
        t.iTotalGSTFee,
        t.PaymentMode
      FROM tblParkwiz_Parking_Data t
      WHERE t.ExitDateTime IS NOT NULL
      ORDER BY t.ExitDateTime DESC
    `;
    
    const recentResult = await db.query(recentQuery);
    console.log('Recent transactions:');
    recentResult.recordset.forEach(txn => {
      console.log(`  ✓ ${txn.VehicleNumber} at ${txn.PlazaName} - ₹${(txn.ParkingFee || 0) + (txn.iTotalGSTFee || 0)} (${txn.PaymentMode})`);
    });
    
    // Test 5: Peak hours analysis
    console.log('\n5. TESTING PEAK HOURS QUERY...');
    
    const peakHoursQuery = `
      SELECT
        DATEPART(HOUR, t.ExitDateTime) as hour,
        COUNT(*) as count
      FROM tblParkwiz_Parking_Data t
      WHERE t.ExitDateTime >= DATEADD(DAY, -7, GETDATE())
      GROUP BY DATEPART(HOUR, t.ExitDateTime)
      ORDER BY count DESC
    `;
    
    const peakResult = await db.query(peakHoursQuery);
    console.log('Peak hours (last 7 days):');
    peakResult.recordset.slice(0, 5).forEach(hour => {
      console.log(`  ✓ ${hour.hour}:00 - ${hour.count} transactions`);
    });
    
    // Test 6: Plaza revenue breakdown
    console.log('\n6. TESTING PLAZA REVENUE QUERY...');
    
    const plazaRevenueQuery = `
      SELECT
        t.PlazaName,
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
        COUNT(*) as transactionCount
      FROM tblParkwiz_Parking_Data t
      WHERE t.ExitDateTime >= DATEADD(DAY, -7, GETDATE())
      GROUP BY t.PlazaName
      ORDER BY totalRevenue DESC
    `;
    
    const plazaResult = await db.query(plazaRevenueQuery);
    console.log('Plaza revenue (last 7 days):');
    plazaResult.recordset.forEach(plaza => {
      console.log(`  ✓ ${plaza.PlazaName}: ₹${plaza.totalRevenue} (${plaza.transactionCount} transactions)`);
    });
    
    // Test 7: Compare data between old and new tables
    console.log('\n7. COMPARING OLD VS NEW TABLE DATA...');
    
    const comparisonQuery = `
      SELECT 
        'tblParkwiz_Parking_Data' as TableName,
        COUNT(*) as RecordCount,
        SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)) as TotalRevenue,
        MIN(ExitDateTime) as EarliestDate,
        MAX(ExitDateTime) as LatestDate
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime IS NOT NULL
      
      UNION ALL
      
      SELECT 
        'tblParkwiz_Parking_Data_OLD' as TableName,
        COUNT(*) as RecordCount,
        SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)) as TotalRevenue,
        MIN(ExitDateTime) as EarliestDate,
        MAX(ExitDateTime) as LatestDate
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime IS NOT NULL
    `;
    
    const comparisonResult = await db.query(comparisonQuery);
    console.log('Table comparison:');
    comparisonResult.recordset.forEach(table => {
      console.log(`  ${table.TableName}:`);
      console.log(`    Records: ${table.RecordCount}`);
      console.log(`    Revenue: ₹${table.TotalRevenue}`);
      console.log(`    Date Range: ${table.EarliestDate} to ${table.LatestDate}`);
    });
    
    console.log('\n=== DASHBOARD TABLE UPDATE TEST COMPLETE ===');
    console.log('✅ All dashboard queries are working with tblParkwiz_Parking_Data');
    console.log('✅ Dashboard controller has been updated successfully');
    console.log('✅ Ready for frontend testing');
    
    process.exit(0);
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
}

testDashboardTableUpdate();
