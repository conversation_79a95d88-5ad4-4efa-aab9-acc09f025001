
/* src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Theme Variables */
:root {
  /* Light Theme (Default) */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f3f4f6;
  --color-bg-sidebar: #ffffff;
  --color-bg-card: #ffffff;
  --color-bg-input: #ffffff;
  --color-bg-button: #f3f4f6;
  --color-bg-hover: #f9fafb;
  --color-bg-active: #e5e7eb;
  --color-bg-dropdown: #ffffff;
  --color-bg-table-header: #f9fafb;
  --color-bg-table-row-even: #ffffff;
  --color-bg-table-row-odd: #f9fafb;
  --color-bg-table-row-hover: #f3f4f6;

  --color-text-primary: #111827;
  --color-text-secondary: #4b5563;
  --color-text-muted: #6b7280;
  --color-text-placeholder: #9ca3af;
  --color-text-inverted: #ffffff;
  --color-text-link: #2563eb;
  --color-text-link-hover: #1d4ed8;

  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;
  --color-border-focus: #3b82f6;

  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-lg: rgba(0, 0, 0, 0.05);

  --color-accent: #3b82f6;
  --color-accent-hover: #2563eb;
  --color-accent-light: #dbeafe;
  --color-accent-dark: #1d4ed8;

  --color-success: #10b981;
  --color-success-light: #d1fae5;
  --color-success-dark: #047857;

  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-warning-dark: #d97706;

  --color-error: #ef4444;
  --color-error-light: #fee2e2;
  --color-error-dark: #b91c1c;

  --color-info: #3b82f6;
  --color-info-light: #dbeafe;
  --color-info-dark: #1d4ed8;
}

/* Dark Theme */
.dark {
  --color-bg-primary: #111827;
  --color-bg-secondary: #1f2937;
  --color-bg-sidebar: #1e293b;
  --color-bg-card: #1f2937;
  --color-bg-input: #374151;
  --color-bg-button: #374151;
  --color-bg-hover: #2d3748;
  --color-bg-active: #4b5563;
  --color-bg-dropdown: #1f2937;
  --color-bg-table-header: #1e293b;
  --color-bg-table-row-even: #1f2937;
  --color-bg-table-row-odd: #111827;
  --color-bg-table-row-hover: #2d3748;

  --color-text-primary: #f9fafb;
  --color-text-secondary: #e5e7eb;
  --color-text-muted: #9ca3af;
  --color-text-placeholder: #6b7280;
  --color-text-inverted: #111827;
  --color-text-link: #60a5fa;
  --color-text-link-hover: #93c5fd;

  --color-border: #374151;
  --color-border-light: #1f2937;
  --color-border-focus: #60a5fa;

  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-lg: rgba(0, 0, 0, 0.5);

  --color-accent: #3b82f6;
  --color-accent-hover: #60a5fa;
  --color-accent-light: #1e40af;
  --color-accent-dark: #93c5fd;

  --color-success: #10b981;
  --color-success-light: #065f46;
  --color-success-dark: #34d399;

  --color-warning: #f59e0b;
  --color-warning-light: #92400e;
  --color-warning-dark: #fbbf24;

  --color-error: #ef4444;
  --color-error-light: #7f1d1d;
  --color-error-dark: #f87171;

  --color-info: #3b82f6;
  --color-info-light: #1e40af;
  --color-info-dark: #93c5fd;
}

/* Parkwiz Brand Theme */
.parkwiz {
  /* Background colors with Parkwiz brand palette */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8f9fa;
  --color-bg-sidebar: #555555; /* Dark gray from logo */
  --color-bg-card: #ffffff;
  --color-bg-input: #ffffff;
  --color-bg-button: #FFD700; /* Golden yellow from logo */
  --color-bg-hover: #FFC107; /* Slightly darker golden yellow */
  --color-bg-active: #FF8F00; /* Darker golden yellow for active states */
  --color-bg-dropdown: #ffffff;
  --color-bg-table-header: #f8f9fa;
  --color-bg-table-row-even: #ffffff;
  --color-bg-table-row-odd: #f8f9fa;
  --color-bg-table-row-hover: #fff3cd;

  /* Text colors */
  --color-text-primary: #212529;
  --color-text-secondary: #555555; /* Dark gray from logo */
  --color-text-muted: #6c757d;
  --color-text-placeholder: #adb5bd;
  --color-text-inverted: #ffffff;
  --color-text-link: #FF8F00;
  --color-text-link-hover: #FFD700;

  /* Border colors */
  --color-border: #dee2e6;
  --color-border-light: #f8f9fa;
  --color-border-focus: #FFD700;

  /* Shadow colors */
  --color-shadow: rgba(85, 85, 85, 0.1);
  --color-shadow-lg: rgba(85, 85, 85, 0.15);

  /* Accent colors - using golden yellow as the primary accent */
  --color-accent: #FFD700;
  --color-accent-hover: #FFC107;
  --color-accent-light: #FFF8DC;
  --color-accent-dark: #FF8F00;

  /* Status colors */
  --color-success: #28a745;
  --color-success-light: #d4edda;
  --color-success-dark: #155724;

  --color-warning: #ffc107;
  --color-warning-light: #fff3cd;
  --color-warning-dark: #856404;

  --color-error: #dc3545;
  --color-error-light: #f8d7da;
  --color-error-dark: #721c24;

  --color-info: #17a2b8;
  --color-info-light: #d1ecf1;
  --color-info-dark: #0c5460;
}

/* Saffron Theme */
.saffron {
  /* Background colors with saffron palette */
  --color-bg-primary: #fff9f0;
  --color-bg-secondary: #fff3e0;
  --color-bg-sidebar: #ff9800;
  --color-bg-card: #ffffff;
  --color-bg-input: #ffffff;
  --color-bg-button: #ff9800;
  --color-bg-hover: #ffb74d;
  --color-bg-active: #f57c00;
  --color-bg-dropdown: #ffffff;
  --color-bg-table-header: #fff3e0;
  --color-bg-table-row-even: #ffffff;
  --color-bg-table-row-odd: #fff9f0;
  --color-bg-table-row-hover: #fff3e0;

  /* Text colors */
  --color-text-primary: #212121;
  --color-text-secondary: #424242;
  --color-text-muted: #757575;
  --color-text-placeholder: #9e9e9e;
  --color-text-inverted: #ffffff;
  --color-text-link: #e65100;
  --color-text-link-hover: #f57c00;

  /* Border colors */
  --color-border: #ffe0b2;
  --color-border-light: #fff3e0;
  --color-border-focus: #ff9800;

  /* Shadow colors */
  --color-shadow: rgba(255, 152, 0, 0.1);
  --color-shadow-lg: rgba(255, 152, 0, 0.15);

  /* Accent colors - using saffron as the primary accent */
  --color-accent: #ff9800;
  --color-accent-hover: #f57c00;
  --color-accent-light: #ffe0b2;
  --color-accent-dark: #e65100;

  /* Status colors */
  --color-success: #4caf50;
  --color-success-light: #c8e6c9;
  --color-success-dark: #2e7d32;

  --color-warning: #ff9800;
  --color-warning-light: #ffe0b2;
  --color-warning-dark: #e65100;

  --color-error: #f44336;
  --color-error-light: #ffcdd2;
  --color-error-dark: #c62828;

  --color-info: #2196f3;
  --color-info-light: #bbdefb;
  --color-info-dark: #0d47a1;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Theme Utility Classes */
.theme-bg-primary { background-color: var(--color-bg-primary); }
.theme-bg-secondary { background-color: var(--color-bg-secondary); }
.theme-bg-sidebar { background-color: var(--color-bg-sidebar); }
.theme-bg-card { background-color: var(--color-bg-card); }
.theme-bg-input { background-color: var(--color-bg-input); }
.theme-bg-button { background-color: var(--color-bg-button); }
.theme-bg-hover { background-color: var(--color-bg-hover); }
.theme-bg-active { background-color: var(--color-bg-active); }
.theme-bg-dropdown { background-color: var(--color-bg-dropdown); }

.theme-text-primary { color: var(--color-text-primary); }
.theme-text-secondary { color: var(--color-text-secondary); }
.theme-text-muted { color: var(--color-text-muted); }
.theme-text-placeholder { color: var(--color-text-placeholder); }
.theme-text-inverted { color: var(--color-text-inverted); }
.theme-text-link { color: var(--color-text-link); }
.theme-text-link-hover { color: var(--color-text-link-hover); }

.theme-border { border-color: var(--color-border); }
.theme-border-light { border-color: var(--color-border-light); }
.theme-border-focus { border-color: var(--color-border-focus); }

.theme-shadow { box-shadow: 0 1px 3px var(--color-shadow); }
.theme-shadow-lg { box-shadow: 0 10px 15px -3px var(--color-shadow-lg); }

.theme-transition {
  transition-property: background-color, border-color, color, fill, stroke, box-shadow;
  transition-duration: 300ms;
  transition-timing-function: ease;
}