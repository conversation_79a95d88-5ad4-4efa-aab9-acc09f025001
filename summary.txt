# PWVMS IIS Setup Summary

## Changes Made

1. Modified web.config:
   - Updated the static files rule to handle .json files and fix path issues
   - Ensured API requests are properly routed to the Node.js backend

2. Fixed frontend/build/index.html:
   - Changed absolute paths (/) to relative paths (./) for static resources
   - Updated the title to "PWVMS - Plaza Toll Management System"

## Next Steps

1. Run the copy_to_production.bat script to copy the modified files to your production environment
   - This will copy web.config and index.html to C:\inetpub\wwwroot\PWVMS

2. Follow the instructions in iis_setup_instructions.txt to:
   - Verify your IIS application setup
   - Install required IIS features
   - Configure your application pool
   - Restart IIS
   - Test your application

3. If you need to rebuild your React frontend:
   - Make sure to configure it to use relative paths by:
     - Adding "homepage": "." to your package.json
     - Or using the PUBLIC_URL environment variable

## Common Issues

1. 403.14 - Directory listing denied:
   - This means IIS can't find a default document (index.html)
   - Make sure your web.config is correctly pointing to frontend/build/index.html

2. 404 - File not found for static resources:
   - Check the paths in your index.html
   - Verify the StaticFiles rule in web.config

3. 500 - Internal server error for API requests:
   - Check that iisnode is properly installed
   - Verify the API rule in web.config
   - Check Node.js permissions and installation