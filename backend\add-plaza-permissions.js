const db = require('./src/config/database');

async function addPlazaPermissions() {
  try {
    console.log('=== ADDING PLAZA MANAGEMENT PERMISSIONS ===');
    
    // Step 1: Get role IDs
    const roles = await db.query('SELECT Id, Name FROM Roles WHERE Name IN (\'CompanyAdmin\', \'PlazaManager\')');
    const companyAdminId = roles.recordset.find(r => r.Name === 'CompanyAdmin')?.Id;
    const plazaManagerId = roles.recordset.find(r => r.Name === 'PlazaManager')?.Id;
    
    console.log('CompanyAdmin ID:', companyAdminId);
    console.log('PlazaManager ID:', plazaManagerId);
    
    // Step 2: Get Plaza Management submodules
    const plazaSubModules = await db.query(`
      SELECT sm.Id, sm.Name, sm.Path
      FROM SubModules sm
      JOIN Modules m ON sm.ModuleId = m.Id
      WHERE m.Name = 'Plaza Management'
    `);
    
    console.log('Plaza Management submodules:', plazaSubModules.recordset);
    
    // Step 3: Create SubModulePermissions for Plaza Management if they don't exist
    console.log('\n1. CREATING SUBMODULE PERMISSIONS...');
    
    for (const subModule of plazaSubModules.recordset) {
      console.log(`\nProcessing ${subModule.Name}...`);
      
      // Get all permission types (View, Create, Edit, Delete)
      const permissions = await db.query('SELECT Id, Name FROM Permissions WHERE Name IN (\'View\', \'Create\', \'Edit\', \'Delete\') ORDER BY Id');
      
      for (const permission of permissions.recordset) {
        // Check if SubModulePermission already exists
        const existingSubModulePermission = await db.query(`
          SELECT Id FROM SubModulePermissions 
          WHERE SubModuleId = ${subModule.Id} AND PermissionId = ${permission.Id}
        `);
        
        if (existingSubModulePermission.recordset.length === 0) {
          // Create SubModulePermission
          await db.query(`
            INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (${subModule.Id}, ${permission.Id}, 1, 1, GETDATE())
          `);
          console.log(`  ✓ Created SubModulePermission: ${subModule.Name} - ${permission.Name}`);
        } else {
          console.log(`  - SubModulePermission already exists: ${subModule.Name} - ${permission.Name}`);
        }
      }
    }
    
    // Step 4: Add RolePermissions for CompanyAdmin
    console.log('\n2. ADDING COMPANYADMIN PERMISSIONS...');
    
    if (companyAdminId) {
      for (const subModule of plazaSubModules.recordset) {
        // Get all SubModulePermissions for this submodule
        const subModulePermissions = await db.query(`
          SELECT smp.Id, p.Name as PermissionName
          FROM SubModulePermissions smp
          JOIN Permissions p ON smp.PermissionId = p.Id
          WHERE smp.SubModuleId = ${subModule.Id} AND smp.IsActive = 1
        `);
        
        for (const smp of subModulePermissions.recordset) {
          // Check if RolePermission already exists
          const existingRolePermission = await db.query(`
            SELECT Id FROM RolePermissions 
            WHERE RoleId = ${companyAdminId} AND SubModulePermissionId = ${smp.Id}
          `);
          
          if (existingRolePermission.recordset.length === 0) {
            // Create RolePermission
            await db.query(`
              INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
              VALUES (${companyAdminId}, ${smp.Id}, 1, 1, GETDATE())
            `);
            console.log(`  ✓ Added CompanyAdmin permission: ${subModule.Name} - ${smp.PermissionName}`);
          } else {
            console.log(`  - CompanyAdmin permission already exists: ${subModule.Name} - ${smp.PermissionName}`);
          }
        }
      }
    }
    
    // Step 5: Add RolePermissions for PlazaManager
    console.log('\n3. ADDING PLAZAMANAGER PERMISSIONS...');
    
    if (plazaManagerId) {
      for (const subModule of plazaSubModules.recordset) {
        // Get all SubModulePermissions for this submodule
        const subModulePermissions = await db.query(`
          SELECT smp.Id, p.Name as PermissionName
          FROM SubModulePermissions smp
          JOIN Permissions p ON smp.PermissionId = p.Id
          WHERE smp.SubModuleId = ${subModule.Id} AND smp.IsActive = 1
        `);
        
        for (const smp of subModulePermissions.recordset) {
          // Check if RolePermission already exists
          const existingRolePermission = await db.query(`
            SELECT Id FROM RolePermissions 
            WHERE RoleId = ${plazaManagerId} AND SubModulePermissionId = ${smp.Id}
          `);
          
          if (existingRolePermission.recordset.length === 0) {
            // Create RolePermission
            await db.query(`
              INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
              VALUES (${plazaManagerId}, ${smp.Id}, 1, 1, GETDATE())
            `);
            console.log(`  ✓ Added PlazaManager permission: ${subModule.Name} - ${smp.PermissionName}`);
          } else {
            console.log(`  - PlazaManager permission already exists: ${subModule.Name} - ${smp.PermissionName}`);
          }
        }
      }
    }
    
    // Step 6: Verify the changes
    console.log('\n4. VERIFICATION...');
    
    const finalPermissionCount = await db.query(`
      SELECT 
        r.Name as RoleName,
        COUNT(*) as TotalPermissions
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      WHERE rp.IsActive = 1
      GROUP BY r.Id, r.Name
      ORDER BY r.Id
    `);
    
    console.log('Updated permission counts:', finalPermissionCount.recordset);
    
    // Check Plaza Management permissions specifically
    const plazaPermissionCheck = await db.query(`
      SELECT 
        r.Name as RoleName,
        sm.Name as SubModuleName,
        p.Name as PermissionName
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE m.Name = 'Plaza Management' AND rp.IsActive = 1
      ORDER BY r.Name, sm.Name, p.Id
    `);
    
    console.log('\nPlaza Management permissions:');
    plazaPermissionCheck.recordset.forEach(perm => {
      console.log(`${perm.RoleName} -> ${perm.SubModuleName}: ${perm.PermissionName}`);
    });
    
    console.log('\n=== PLAZA PERMISSIONS ADDED SUCCESSFULLY ===');
    console.log('\nCompanyAdmin and PlazaManager should now have access to:');
    console.log('- Plazas (View, Create, Edit, Delete)');
    console.log('- Plaza Settings (View, Create, Edit, Delete)');
    
    process.exit(0);
  } catch (error) {
    console.error('Adding plaza permissions failed:', error);
    process.exit(1);
  }
}

addPlazaPermissions();
