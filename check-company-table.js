// Check the structure of tblCompanyMaster table

const db = require('./backend/src/config/database');

async function checkCompanyTable() {
  try {
    console.log('=== CHECKING COMPANY TABLE STRUCTURE ===');
    
    // Check tblCompanyMaster table structure
    const companyStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblCompanyMaster'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('tblCompanyMaster table structure:');
    companyStructure.recordset.forEach(col => {
      console.log(`  ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.IS_NULLABLE}`);
    });
    
    // Check sample data
    console.log('\nSample company data:');
    const sampleData = await db.query('SELECT TOP 5 * FROM tblCompanyMaster');
    console.log(sampleData.recordset);
    
    process.exit(0);
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

checkCompanyTable();