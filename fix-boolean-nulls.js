// Fix script to handle boolean fields that don't allow NULL values
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'backend/src/controllers/LaneController.js');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// List of boolean fields that likely don't allow NULL in database
const booleanFields = [
  'DisplayPole',
  'CashDrawer', 
  'MultipleExit',
  'Antipassback',
  'HFPasscard',
  'APS_Exit',
  'flgKioskCamera',
  'flgReceiptPrint',
  'flgGKeyDetails',
  'flgCKeyCard',
  'flgP4S',
  'flgPasscard',
  'PayTmPG',
  'FlgLPRCamera',
  'flgPaperSensor',
  'fRecyclerStatus',
  'flgCCUpdateEx',
  'flgSubLane'
];

// Replace each boolean field in the createLane function (first occurrence)
booleanFields.forEach(field => {
  const oldPattern = `        ${field}: ${field} || null,`;
  const newPattern = `        ${field}: ${field} !== undefined ? ${field} : false,`;
  
  const firstIndex = content.indexOf(oldPattern);
  if (firstIndex !== -1) {
    content = content.substring(0, firstIndex) + newPattern + content.substring(firstIndex + oldPattern.length);
    console.log(`✅ Fixed ${field} field`);
  } else {
    console.log(`⚠️  Could not find ${field} field pattern`);
  }
});

// Write the file back
fs.writeFileSync(filePath, content, 'utf8');
console.log('✅ All boolean fields updated successfully');
console.log('Please restart your backend server to apply the changes.');