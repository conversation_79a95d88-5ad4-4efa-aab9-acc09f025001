@echo off
echo Copying files to production environment...

REM Create directories if they don't exist
if not exist "C:\inetpub\wwwroot\PWVMS\frontend\build" mkdir "C:\inetpub\wwwroot\PWVMS\frontend\build"
if not exist "C:\inetpub\wwwroot\PWVMS\backend\src" mkdir "C:\inetpub\wwwroot\PWVMS\backend\src"

REM Copy web.config
copy /Y "d:\PWVMS\web.config" "C:\inetpub\wwwroot\PWVMS\web.config"
echo Copied web.config

REM Copy frontend files
copy /Y "d:\PWVMS\frontend\build\index.html" "C:\inetpub\wwwroot\PWVMS\frontend\build\index.html"
echo Copied frontend/build/index.html

REM Copy entire frontend/build directory if needed
REM xcopy /E /Y "d:\PWVMS\frontend\build" "C:\inetpub\wwwroot\PWVMS\frontend\build\"
REM echo Copied frontend/build directory

echo.
echo Files copied successfully!
echo.
echo Please restart IIS to apply changes:
echo iisreset
echo.
pause