// Investigate the filtering issue found in the dashboard
// Check why accr user can only access 2 plazas when assigned to 5

const db = require('./backend/src/config/database');

async function investigateFilteringIssue() {
  try {
    console.log('=== INVESTIGATING DASHBOARD FILTERING ISSUE ===\n');
    
    const accrUserId = 5;
    
    // 1. Check all plaza assignments for accr
    console.log('1. CHECKING PLAZA ASSIGNMENTS');
    console.log('=============================');
    
    const assignmentsQuery = `
      SELECT 
        up.Id as AssignmentId,
        up.UserId,
        up.PlazaId,
        up.IsActive as AssignmentActive,
        up.CreatedOn,
        p.PlazaName,
        p.PlazaCode,
        p.IsActive as PlazaActive,
        c.CompanyName
      FROM UserPlaza up
      INNER JOIN Plaza p ON up.PlazaId = p.Id
      INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE up.UserId = @userId
      ORDER BY up.CreatedOn DESC
    `;
    
    const assignments = await db.query(assignmentsQuery, { userId: accrUserId });
    
    console.log(`Total plaza assignments for accr: ${assignments.recordset.length}`);
    assignments.recordset.forEach(assignment => {
      console.log(`  • ${assignment.PlazaName} (${assignment.PlazaCode})`);
      console.log(`    Assignment Active: ${assignment.AssignmentActive}`);
      console.log(`    Plaza Active: ${assignment.PlazaActive}`);
      console.log(`    Company: ${assignment.CompanyName}`);
      console.log(`    Assigned On: ${assignment.CreatedOn}`);
      console.log('');
    });
    
    // 2. Check which plazas have transaction data
    console.log('2. CHECKING TRANSACTION DATA AVAILABILITY');
    console.log('=========================================');
    
    const transactionDataQuery = `
      SELECT 
        p.PlazaName,
        p.PlazaCode,
        COUNT(t.PakringDataID) as TransactionCount,
        MIN(t.ExitDateTime) as FirstTransaction,
        MAX(t.ExitDateTime) as LastTransaction
      FROM Plaza p
      INNER JOIN UserPlaza up ON p.Id = up.PlazaId
      LEFT JOIN tblParkwiz_Parking_Data t ON p.PlazaCode = t.PlazaCode
      WHERE up.UserId = @userId AND up.IsActive = 1
      GROUP BY p.PlazaName, p.PlazaCode
      ORDER BY TransactionCount DESC
    `;
    
    const transactionData = await db.query(transactionDataQuery, { userId: accrUserId });
    
    console.log('Transaction data for assigned plazas:');
    transactionData.recordset.forEach(data => {
      console.log(`  • ${data.PlazaName} (${data.PlazaCode}): ${data.TransactionCount} transactions`);
      if (data.TransactionCount > 0) {
        console.log(`    First: ${data.FirstTransaction}`);
        console.log(`    Last: ${data.LastTransaction}`);
      } else {
        console.log(`    No transaction data found`);
      }
      console.log('');
    });
    
    // 3. Check the exact filtering query used in dashboard
    console.log('3. TESTING DASHBOARD FILTERING QUERY');
    console.log('====================================');
    
    const dashboardFilterQuery = `
      SELECT 
        p.PlazaCode,
        p.PlazaName,
        p.IsActive as PlazaActive,
        up.IsActive as AssignmentActive,
        COUNT(t.PakringDataID) as TransactionCount
      FROM Plaza p 
      JOIN UserPlaza up ON p.Id = up.PlazaId 
      LEFT JOIN tblParkwiz_Parking_Data t ON p.PlazaCode = t.PlazaCode
      WHERE up.UserId = @userId AND up.IsActive = 1
      GROUP BY p.PlazaCode, p.PlazaName, p.IsActive, up.IsActive
      ORDER BY p.PlazaName
    `;
    
    const dashboardFilter = await db.query(dashboardFilterQuery, { userId: accrUserId });
    
    console.log('Dashboard filtering results:');
    dashboardFilter.recordset.forEach(data => {
      console.log(`  • ${data.PlazaName} (${data.PlazaCode})`);
      console.log(`    Plaza Active: ${data.PlazaActive}`);
      console.log(`    Assignment Active: ${data.AssignmentActive}`);
      console.log(`    Transactions: ${data.TransactionCount}`);
      console.log('');
    });
    
    // 4. Check if there's a mismatch in PlazaCode
    console.log('4. CHECKING PLAZA CODE CONSISTENCY');
    console.log('==================================');
    
    const plazaCodeQuery = `
      SELECT DISTINCT
        p.Id as PlazaId,
        p.PlazaName,
        p.PlazaCode as PlazaTableCode,
        (SELECT DISTINCT TOP 1 t.PlazaCode FROM tblParkwiz_Parking_Data t WHERE t.PlazaCode = p.PlazaCode) as TransactionTableCode,
        (SELECT COUNT(*) FROM tblParkwiz_Parking_Data t WHERE t.PlazaCode = p.PlazaCode) as TransactionCount
      FROM Plaza p
      INNER JOIN UserPlaza up ON p.Id = up.PlazaId
      WHERE up.UserId = @userId AND up.IsActive = 1
      ORDER BY p.PlazaName
    `;
    
    const plazaCodes = await db.query(plazaCodeQuery, { userId: accrUserId });
    
    console.log('Plaza code consistency check:');
    plazaCodes.recordset.forEach(data => {
      const codeMatch = data.PlazaTableCode === data.TransactionTableCode;
      console.log(`  • ${data.PlazaName}`);
      console.log(`    Plaza Table Code: ${data.PlazaTableCode}`);
      console.log(`    Transaction Table Code: ${data.TransactionTableCode || 'NOT FOUND'}`);
      console.log(`    Code Match: ${codeMatch ? '✅' : '❌'}`);
      console.log(`    Transaction Count: ${data.TransactionCount}`);
      console.log('');
    });
    
    // 5. Check recent transactions for each plaza
    console.log('5. CHECKING RECENT TRANSACTIONS');
    console.log('===============================');
    
    const recentTransactionsQuery = `
      SELECT 
        p.PlazaName,
        p.PlazaCode,
        COUNT(t.PakringDataID) as Last30Days,
        COUNT(CASE WHEN t.ExitDateTime >= DATEADD(DAY, -7, GETDATE()) THEN 1 END) as Last7Days,
        COUNT(CASE WHEN t.ExitDateTime >= DATEADD(DAY, -1, GETDATE()) THEN 1 END) as Last24Hours
      FROM Plaza p
      INNER JOIN UserPlaza up ON p.Id = up.PlazaId
      LEFT JOIN tblParkwiz_Parking_Data t ON p.PlazaCode = t.PlazaCode 
        AND t.ExitDateTime >= DATEADD(DAY, -30, GETDATE())
      WHERE up.UserId = @userId AND up.IsActive = 1
      GROUP BY p.PlazaName, p.PlazaCode
      ORDER BY Last30Days DESC
    `;
    
    const recentTransactions = await db.query(recentTransactionsQuery, { userId: accrUserId });
    
    console.log('Recent transaction activity:');
    recentTransactions.recordset.forEach(data => {
      console.log(`  • ${data.PlazaName} (${data.PlazaCode})`);
      console.log(`    Last 30 days: ${data.Last30Days} transactions`);
      console.log(`    Last 7 days: ${data.Last7Days} transactions`);
      console.log(`    Last 24 hours: ${data.Last24Hours} transactions`);
      console.log('');
    });
    
    // 6. Test the exact dashboard controller query
    console.log('6. TESTING EXACT DASHBOARD CONTROLLER QUERY');
    console.log('===========================================');
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    const endDate = new Date();
    
    const exactDashboardQuery = `
      SELECT
        t.PlazaCode,
        t.PlazaName,
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
        COUNT(*) as TransactionCount,
        COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)
      GROUP BY t.PlazaCode, t.PlazaName
      ORDER BY TotalRevenue DESC
    `;
    
    const exactDashboard = await db.query(exactDashboardQuery, { 
      userId: accrUserId, 
      startDate, 
      endDate 
    });
    
    console.log('Exact dashboard controller results:');
    if (exactDashboard.recordset.length > 0) {
      exactDashboard.recordset.forEach(data => {
        console.log(`  • ${data.PlazaName} (${data.PlazaCode})`);
        console.log(`    Revenue: ₹${data.TotalRevenue}`);
        console.log(`    Transactions: ${data.TransactionCount}`);
        console.log(`    Vehicles: ${data.VehicleCount}`);
        console.log(`    Avg Duration: ${data.AvgDuration} minutes`);
        console.log('');
      });
    } else {
      console.log('  No data returned by dashboard query');
    }
    
    // 7. Summary and recommendations
    console.log('7. SUMMARY AND RECOMMENDATIONS');
    console.log('==============================');
    
    const assignedPlazas = assignments.recordset.filter(a => a.AssignmentActive && a.PlazaActive).length;
    const plazasWithData = exactDashboard.recordset.length;
    
    console.log(`\nSummary:`);
    console.log(`  • User assigned to ${assignedPlazas} active plazas`);
    console.log(`  • Dashboard shows data for ${plazasWithData} plazas`);
    console.log(`  • Missing data for ${assignedPlazas - plazasWithData} plazas`);
    
    if (assignedPlazas !== plazasWithData) {
      console.log(`\n⚠️  ISSUE IDENTIFIED:`);
      console.log(`  The dashboard filtering is working correctly, but some assigned plazas have no transaction data.`);
      console.log(`  This could be because:`);
      console.log(`  1. The plazas are newly created and have no transactions yet`);
      console.log(`  2. There's a mismatch in PlazaCode between Plaza table and transaction table`);
      console.log(`  3. The transaction data is outside the date range being queried`);
      
      console.log(`\n✅ FILTERING STATUS:`);
      console.log(`  The role-based filtering logic is working correctly.`);
      console.log(`  PlazaManager can only see data from assigned plazas.`);
      console.log(`  The "missing" plazas simply have no transaction data to display.`);
    } else {
      console.log(`\n✅ FILTERING IS WORKING PERFECTLY!`);
    }
    
    console.log('\n=== INVESTIGATION COMPLETE ===');
    process.exit(0);
    
  } catch (error) {
    console.error('Error during investigation:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

investigateFilteringIssue();