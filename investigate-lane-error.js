// <PERSON>ript to investigate the lane creation error
// Check table structure and identify the TypeCode column issue

const db = require('./backend/src/config/database');

async function investigateLaneError() {
  try {
    console.log('=== INVESTIGATING LANE CREATION ERROR ===\n');
    
    // 1. Check the structure of tblLaneDetails table
    console.log('1. CHECKING tblLaneDetails TABLE STRUCTURE');
    console.log('==========================================');
    
    const tableStructureQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        CHARACTER_MAXIMUM_LENGTH,
        IS_NULLABLE,
        COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblLaneDetails' 
      ORDER BY ORDINAL_POSITION
    `;
    
    const tableStructure = await db.query(tableStructureQuery);
    
    console.log('Table structure for tblLaneDetails:');
    tableStructure.recordset.forEach(column => {
      const length = column.CHARACTER_MAXIMUM_LENGTH ? `(${column.CHARACTER_MAXIMUM_LENGTH})` : '';
      const nullable = column.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
      const defaultValue = column.COLUMN_DEFAULT ? ` DEFAULT ${column.COLUMN_DEFAULT}` : '';
      
      console.log(`  ${column.COLUMN_NAME}: ${column.DATA_TYPE}${length} ${nullable}${defaultValue}`);
    });
    
    // 2. Focus on TypeCode column
    console.log('\n2. ANALYZING TypeCode COLUMN');
    console.log('============================');
    
    const typeCodeColumn = tableStructure.recordset.find(col => col.COLUMN_NAME === 'TypeCode');
    if (typeCodeColumn) {
      console.log(`TypeCode column details:`);
      console.log(`  Data Type: ${typeCodeColumn.DATA_TYPE}`);
      console.log(`  Max Length: ${typeCodeColumn.CHARACTER_MAXIMUM_LENGTH}`);
      console.log(`  Nullable: ${typeCodeColumn.IS_NULLABLE}`);
      console.log(`  Default: ${typeCodeColumn.COLUMN_DEFAULT || 'None'}`);
      
      if (typeCodeColumn.CHARACTER_MAXIMUM_LENGTH < 10) {
        console.log(`\n⚠️  ISSUE IDENTIFIED:`);
        console.log(`  TypeCode column is too small (${typeCodeColumn.CHARACTER_MAXIMUM_LENGTH} characters)`);
        console.log(`  The value '00' is being truncated, suggesting the column needs to be larger`);
      }
    } else {
      console.log('❌ TypeCode column not found in table structure');
    }
    
    // 3. Check existing data in TypeCode column
    console.log('\n3. CHECKING EXISTING TypeCode VALUES');
    console.log('===================================');
    
    const existingDataQuery = `
      SELECT 
        TypeCode,
        COUNT(*) as Count,
        LEN(TypeCode) as Length
      FROM tblLaneDetails 
      WHERE TypeCode IS NOT NULL
      GROUP BY TypeCode
      ORDER BY COUNT(*) DESC
    `;
    
    const existingData = await db.query(existingDataQuery);
    
    if (existingData.recordset.length > 0) {
      console.log('Existing TypeCode values:');
      existingData.recordset.forEach(data => {
        console.log(`  '${data.TypeCode}' (Length: ${data.Length}, Count: ${data.Count})`);
      });
    } else {
      console.log('No existing TypeCode values found');
    }
    
    // 4. Check lane creation code
    console.log('\n4. CHECKING LANE CREATION LOGIC');
    console.log('===============================');
    
    // Look for lane creation in controllers
    const fs = require('fs');
    const path = require('path');
    
    try {
      const laneControllerPath = path.join(__dirname, 'backend/src/controllers/LaneController.js');
      if (fs.existsSync(laneControllerPath)) {
        const laneController = fs.readFileSync(laneControllerPath, 'utf8');
        
        // Look for INSERT statements
        const insertMatches = laneController.match(/INSERT INTO.*tblLaneDetails.*TypeCode/gi);
        if (insertMatches) {
          console.log('Found INSERT statements with TypeCode:');
          insertMatches.forEach((match, index) => {
            console.log(`  ${index + 1}. ${match.substring(0, 100)}...`);
          });
        }
        
        // Look for TypeCode assignments
        const typeCodeMatches = laneController.match(/TypeCode.*=.*['"`].*['"`]/gi);
        if (typeCodeMatches) {
          console.log('\nFound TypeCode assignments:');
          typeCodeMatches.forEach((match, index) => {
            console.log(`  ${index + 1}. ${match}`);
          });
        }
      } else {
        console.log('LaneController.js not found');
      }
    } catch (error) {
      console.log(`Error reading lane controller: ${error.message}`);
    }
    
    // 5. Suggest solutions
    console.log('\n5. SUGGESTED SOLUTIONS');
    console.log('======================');
    
    if (typeCodeColumn && typeCodeColumn.CHARACTER_MAXIMUM_LENGTH < 10) {
      console.log('Option 1: Increase TypeCode column size');
      console.log(`ALTER TABLE tblLaneDetails ALTER COLUMN TypeCode VARCHAR(10);`);
      console.log('');
      
      console.log('Option 2: Check the value being inserted');
      console.log('- Verify what value is being passed for TypeCode');
      console.log('- Ensure it matches the expected format');
      console.log('- Consider using shorter codes if appropriate');
      console.log('');
      
      console.log('Option 3: Make TypeCode nullable if not required');
      console.log(`ALTER TABLE tblLaneDetails ALTER COLUMN TypeCode VARCHAR(${typeCodeColumn.CHARACTER_MAXIMUM_LENGTH}) NULL;`);
    }
    
    // 6. Check for lane types or categories
    console.log('\n6. CHECKING LANE TYPES/CATEGORIES');
    console.log('=================================');
    
    const laneTypesQuery = `
      SELECT 
        LaneType,
        COUNT(*) as Count
      FROM tblLaneDetails 
      WHERE LaneType IS NOT NULL
      GROUP BY LaneType
      ORDER BY COUNT(*) DESC
    `;
    
    const laneTypes = await db.query(laneTypesQuery);
    
    if (laneTypes.recordset.length > 0) {
      console.log('Existing lane types:');
      laneTypes.recordset.forEach(type => {
        console.log(`  ${type.LaneType}: ${type.Count} lanes`);
      });
    } else {
      console.log('No lane type data found');
    }
    
    console.log('\n=== INVESTIGATION COMPLETE ===');
    process.exit(0);
    
  } catch (error) {
    console.error('Error during investigation:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

investigateLaneError();