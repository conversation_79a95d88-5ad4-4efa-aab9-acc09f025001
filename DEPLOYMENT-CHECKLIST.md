# PWVMS Deployment Checklist

## Pre-Deployment Checklist

- [ ] Windows Server with IIS installed
- [ ] Node.js LTS version installed
- [ ] URL Rewrite Module for IIS installed
- [ ] iisnode v0.2.26 installed
- [ ] SQL Server database created and accessible
- [ ] Firewall configured to allow HTTP/HTTPS traffic
- [ ] SSL certificate obtained (for production)

## Deployment Steps Checklist

1. **Prepare Deployment Package**
   - [ ] Extract the PWVMS deployment package to a temporary location

2. **Configure Environment**
   - [ ] Navigate to the backend directory
   - [ ] Copy `.env.template` to `.env`
   - [ ] Update database connection settings in `.env`
   - [ ] Set JWT_SECRET to a secure value
   - [ ] Configure FRONTEND_URL to match your domain

3. **Run Deployment Script**
   - [ ] Open PowerShell as Administrator
   - [ ] Navigate to the extracted package directory
   - [ ] Run `.\deploy-to-iis.ps1`
   - [ ] Verify script completes without errors

4. **Verify IIS Configuration**
   - [ ] PWVMS website created in IIS
   - [ ] PWVMS_AppPool application pool running
   - [ ] Website bindings configured correctly
   - [ ] Handler mappings include iisnode

5. **Configure HTTPS (Production)**
   - [ ] Install SSL certificate
   - [ ] Add HTTPS binding in IIS
   - [ ] Configure HTTP to HTTPS redirection

## Post-Deployment Checklist

1. **Verify Application**
   - [ ] Frontend loads correctly
   - [ ] API health check returns success
   - [ ] User authentication works
   - [ ] File uploads function correctly

2. **Security Configuration**
   - [ ] Remove unnecessary file access permissions
   - [ ] Verify `.env` file is not web-accessible
   - [ ] Configure IIS request filtering
   - [ ] Set up IP restrictions if needed

3. **Backup Configuration**
   - [ ] Set up database backup schedule
   - [ ] Configure application files backup
   - [ ] Test restore procedure

4. **Monitoring Setup**
   - [ ] Configure IIS logging
   - [ ] Set up application performance monitoring
   - [ ] Configure alerts for critical errors

## Troubleshooting Reference

- IIS Logs: `C:\inetpub\logs\LogFiles\W3SVC1\`
- Node.js Logs: `C:\inetpub\wwwroot\PWVMS\iisnode\`
- Application Logs: `C:\inetpub\wwwroot\PWVMS\backend\logs\`

## Contact Information

For deployment assistance, contact:
- [Your contact information]