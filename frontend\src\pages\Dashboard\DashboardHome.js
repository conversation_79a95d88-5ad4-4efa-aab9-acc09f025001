import { useState, useEffect, lazy, Suspense, useMemo, useCallback } from 'react';
import { useAuth } from '../../contexts/authContext';
import { DashboardCard } from '../../components/DashboardCard';
import {
  CreditCard,
  Clock,
  Car,
  Activity,
  BarChart3,
  TrendingUp
} from 'lucide-react';
import dashboardApi from '../../api/dashboardApi';

// Lazy load heavy components for better performance
const D3Chart = lazy(() => import('../../components/D3Chart').then(module => ({ default: module.D3Chart })));
const RecentActivities = lazy(() => import('../../components/RecentActivities').then(module => ({ default: module.RecentActivities })));
const AdvancedFilter = lazy(() => import('../../components/AdvancedFilter').then(module => ({ default: module.AdvancedFilter })));



/**
 * DashboardHome Component
 * 
 * Main dashboard page showing key metrics and visualizations
 * based on user role and permissions
 */
const DashboardHome = () => {
  const { } = useAuth(); // Auth context available for future use
  const [dateRange, setDateRange] = useState('today');
  const [selectedEntity, setSelectedEntity] = useState({});
  const [chartType, setChartType] = useState('bar');

  // Advanced filter state
  const [filters, setFilters] = useState({
    dateRange: 'today',
    companyId: '',
    plazaId: '',
    paymentMethod: '',
    vehicleType: '',
    customStartDate: '',
    customEndDate: ''
  });
  const [dashboardData, setDashboardData] = useState({
    summary: {},
    revenueByPaymentMethod: [],
    recentTransactions: [],
    peakHours: [],
    loading: true,
    error: null
  });

  // Cache for dashboard data to avoid unnecessary API calls
  const [dataCache, setDataCache] = useState(new Map());

  // Generate cache key based on current filters
  const cacheKey = useMemo(() => {
    return JSON.stringify({ filters, dateRange, selectedEntity });
  }, [filters, dateRange, selectedEntity]);

  // Function to fetch all dashboard data with progressive loading and caching
  const fetchDashboardData = useCallback(async () => {
    // Check cache first
    if (dataCache.has(cacheKey)) {
      const cachedData = dataCache.get(cacheKey);
      // Check if cache is still fresh (5 minutes)
      if (Date.now() - cachedData.timestamp < 5 * 60 * 1000) {
        setDashboardData(cachedData.data);
        return;
      }
    }
    try {
      setDashboardData(prev => ({ ...prev, loading: true }));
      
      // Initialize an object to store our data as it comes in
      const newData = {};
      
      // Use Promise.all to fetch data in parallel but handle individual failures
      const fetchPromises = [
        // Fetch summary data (highest priority)
        dashboardApi.getDashboardSummary({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.summary = response.data.data;
          // Update state as soon as summary data is available
          setDashboardData(prev => ({ 
            ...prev, 
            summary: response.data.data,
            loading: false 
          }));
        })
        .catch(error => {
          console.error('Error fetching summary data:', error);
          newData.summaryError = 'Failed to load summary data';
        }),
        
        // Fetch recent transactions (medium priority)
        dashboardApi.getRecentTransactions({
          ...selectedEntity,
          limit: 5
        })
        .then(response => {
          newData.recentTransactions = response.data.data;
          // Update state as soon as transaction data is available
          setDashboardData(prev => ({ 
            ...prev, 
            recentTransactions: response.data.data 
          }));
        })
        .catch(error => {
          console.error('Error fetching recent transactions:', error);
          newData.transactionsError = 'Failed to load recent transactions';
        }),
        
        // Fetch revenue by payment method (lower priority)
        dashboardApi.getRevenueByPaymentMethod({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.revenueByPaymentMethod = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({ 
            ...prev, 
            revenueByPaymentMethod: response.data.data 
          }));
        })
        .catch(error => {
          console.error('Error fetching payment method data:', error);
          newData.paymentMethodError = 'Failed to load payment method data';
        }),
        
        // Fetch peak hours data (lowest priority)
        dashboardApi.getPeakHoursData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.peakHours = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({ 
            ...prev, 
            peakHours: response.data.data 
          }));
        })
        .catch(error => {
          console.error('Error fetching peak hours data:', error);
          newData.peakHoursError = 'Failed to load peak hours data';
        })
      ];
      
      // Wait for all promises to settle (either resolve or reject)
      await Promise.allSettled(fetchPromises);
      
      // Final update to ensure all data is in sync and loading state is false
      const finalData = {
        ...newData,
        loading: false,
        // Only set overall error if all requests failed
        error: Object.keys(newData).length === 0 ? 'Failed to load dashboard data' : null
      };

      setDashboardData(prev => ({
        ...prev,
        ...finalData
      }));

      // Cache the successful data
      if (Object.keys(newData).length > 0) {
        setDataCache(prev => new Map(prev.set(cacheKey, {
          data: { ...dashboardData, ...finalData },
          timestamp: Date.now()
        })));
      }

    } catch (error) {
      console.error('Error in dashboard data fetching:', error);
      setDashboardData(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load dashboard data'
      }));
    }
  }, [cacheKey, dataCache, dashboardData, dateRange, selectedEntity]);

  // Fetch dashboard data when filters change
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Format recent transactions for the activity feed
  const formatRecentActivities = () => {
    if (!dashboardData.recentTransactions) return [];
    
    return dashboardData.recentTransactions.map(transaction => ({
      title: `Vehicle ${transaction.VehicleNumber || 'Unknown'}`,
      time: new Date(transaction.ExitDateTime).toLocaleString(),
      value: `₹${(transaction.ParkingFee + transaction.iTotalGSTFee).toFixed(2)}`,
      color: getPaymentMethodColor(transaction.PaymentMode),
      details: `${transaction.PlazaName} - ${transaction.ExitLane}`
    }));
  };

  // Get color based on payment method
  const getPaymentMethodColor = (method) => {
    switch(method?.toLowerCase()) {
      case 'cash': return 'bg-green-500';
      case 'fastag': return 'bg-blue-500';
      case 'upi': return 'bg-purple-500';
      case 'card': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  // Render loading state
  if (dashboardData.loading) {
    return (
      <div className="p-6 flex justify-center items-center h-64">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-200 rounded-full mb-4"></div>
          <div className="h-4 w-32 bg-blue-200 rounded mb-2"></div>
          <div className="h-3 w-24 bg-blue-100 rounded"></div>
        </div>
      </div>
    );
  }

  // Render error state
  if (dashboardData.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error</p>
          <p>{dashboardData.error}</p>
          <button 
            onClick={fetchDashboardData}
            className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Handle filter changes from AdvancedFilter component
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    // Update legacy state for backward compatibility
    setDateRange(newFilters.dateRange);
    if (newFilters.companyId || newFilters.plazaId) {
      setSelectedEntity({
        companyId: newFilters.companyId,
        plazaId: newFilters.plazaId
      });
    }
  };

  return (
    <div className="min-h-screen p-4 lg:p-6" style={{ backgroundColor: 'var(--color-bg-primary)' }}>
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Enhanced Header with Visual Hierarchy */}
        <div className="relative">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <h1
                className="text-3xl lg:text-4xl font-bold mb-2"
                style={{ color: 'var(--color-text-primary)' }}
              >
                Dashboard Overview
              </h1>
              <p
                className="text-sm lg:text-base"
                style={{ color: 'var(--color-text-secondary)' }}
              >
                Real-time insights and analytics for your parking operations
              </p>
            </div>
            <div className="flex items-center gap-3">
              <div
                className="px-4 py-2 rounded-lg border"
                style={{
                  backgroundColor: 'var(--color-bg-card)',
                  borderColor: 'var(--color-border)'
                }}
              >
                <span
                  className="text-sm font-medium"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Last Updated: {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
          {/* Decorative accent line */}
          <div
            className="absolute bottom-0 left-0 h-1 w-24 rounded-full"
            style={{ backgroundColor: 'var(--color-accent)' }}
          ></div>
        </div>
        
        {/* Advanced Filters Section */}
        <Suspense fallback={
          <div className="flex justify-center items-center h-16 mb-6">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2" style={{ borderColor: 'var(--color-accent)' }}></div>
          </div>
        }>
          <AdvancedFilter
            filters={filters}
            onChange={handleFilterChange}
            className="mb-6"
          />
        </Suspense>

        {/* Enhanced KPI Cards Section */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <h2
              className="text-xl lg:text-2xl font-semibold"
              style={{ color: 'var(--color-text-primary)' }}
            >
              Key Performance Indicators
            </h2>
            <div
              className="flex-1 h-px"
              style={{ backgroundColor: 'var(--color-border)' }}
            ></div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6">
            <DashboardCard
              title="Total Revenue"
              value={`₹${dashboardData.summary.totalRevenue?.toLocaleString() || '0'}`}
              trend={dashboardData.summary.revenueTrend}
              icon={CreditCard}
              themeColor="primary"
            />
            <DashboardCard
              title="Total Transactions"
              value={dashboardData.summary.transactionCount?.toLocaleString() || '0'}
              trend={dashboardData.summary.transactionTrend}
              icon={Activity}
              themeColor="success"
            />
            <DashboardCard
              title="Vehicles Processed"
              value={dashboardData.summary.vehicleCount?.toLocaleString() || '0'}
              trend={dashboardData.summary.vehicleTrend}
              icon={Car}
              themeColor="info"
            />
            <DashboardCard
              title="Avg. Parking Duration"
              value={formatDuration(dashboardData.summary.avgDuration)}
              trend={dashboardData.summary.durationTrend}
              icon={Clock}
              themeColor="secondary"
            />
          </div>
        </div>

        {/* Analytics Section */}
        <div className="space-y-8">
          <div className="flex items-center gap-3">
            <h2
              className="text-xl lg:text-2xl font-semibold"
              style={{ color: 'var(--color-text-primary)' }}
            >
              Analytics & Insights
            </h2>
            <div
              className="flex-1 h-px"
              style={{ backgroundColor: 'var(--color-border)' }}
            ></div>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Enhanced Transaction Chart */}
            <div
              className="xl:col-span-2 rounded-xl shadow-lg p-6 lg:p-8 border"
              style={{
                backgroundColor: 'var(--color-bg-card)',
                borderColor: 'var(--color-border)'
              }}
            >
            {dashboardData.peakHours && dashboardData.peakHours.length > 0 ? (
              <>
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3">
                  <h2
                    className="text-lg lg:text-xl font-semibold flex items-center"
                    style={{ color: 'var(--color-text-primary)' }}
                  >
                    <BarChart3 className="w-5 h-5 mr-2" style={{ color: 'var(--color-accent)' }} />
                    Transaction Overview
                  </h2>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setChartType('line')}
                      className={`px-3 py-1.5 text-sm rounded-lg transition-all duration-200 ${
                        chartType === 'line'
                          ? 'text-white shadow-md'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                      style={{
                        backgroundColor: chartType === 'line' ? 'var(--color-accent)' : 'var(--color-bg-button)',
                        borderColor: 'var(--color-border)'
                      }}
                    >
                      Line
                    </button>
                    <button
                      onClick={() => setChartType('bar')}
                      className={`px-3 py-1.5 text-sm rounded-lg transition-all duration-200 ${
                        chartType === 'bar'
                          ? 'text-white shadow-md'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                      style={{
                        backgroundColor: chartType === 'bar' ? 'var(--color-accent)' : 'var(--color-bg-button)',
                        borderColor: 'var(--color-border)'
                      }}
                    >
                      Bar
                    </button>
                  </div>
                </div>
                <div className="relative">
                  <Suspense fallback={
                    <div className="flex justify-center items-center h-64">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--color-accent)' }}></div>
                    </div>
                  }>
                    <D3Chart
                      type={chartType}
                      data={dashboardData.peakHours.map(hour => ({
                        label: `${hour.hour}:00`,
                        value: hour.count
                      }))}
                      options={{
                        height: window.innerWidth < 768 ? 250 : 350, // Responsive height
                        xKey: "label",
                        yKey: "value",
                        animate: true,
                        showGrid: true,
                        curve: "cardinal",
                        barPadding: 0.2,
                        margin: {
                          top: 20,
                          right: 30,
                          bottom: window.innerWidth < 768 ? 60 : 40, // More bottom margin on mobile
                          left: 50
                        },
                        responsive: true
                      }}
                    />
                  </Suspense>
                </div>
              </>
            ) : (
              <div
                className="flex flex-col justify-center items-center h-64 rounded-lg"
                style={{ backgroundColor: 'var(--color-bg-secondary)' }}
              >
                <TrendingUp
                  className="w-12 h-12 mb-3 opacity-50"
                  style={{ color: 'var(--color-text-muted)' }}
                />
                <p style={{ color: 'var(--color-text-muted)' }}>
                  No transaction data available for the selected period
                </p>
              </div>
            )}
          </div>

          {/* Enhanced Recent Activities */}
          <div
            className="rounded-xl shadow-lg p-6 lg:p-8 border"
            style={{
              backgroundColor: 'var(--color-bg-card)',
              borderColor: 'var(--color-border)'
            }}
          >
            <h2
              className="text-lg lg:text-xl font-semibold mb-4 flex items-center"
              style={{ color: 'var(--color-text-primary)' }}
            >
              <Activity className="w-5 h-5 mr-2" style={{ color: 'var(--color-accent)' }} />
              Recent Transactions
            </h2>
            {dashboardData.recentTransactions && dashboardData.recentTransactions.length > 0 ? (
              <Suspense fallback={
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--color-accent)' }}></div>
                </div>
              }>
                <RecentActivities activities={formatRecentActivities()} />
              </Suspense>
            ) : (
              <div
                className="flex flex-col justify-center items-center h-64 rounded-lg"
                style={{ backgroundColor: 'var(--color-bg-secondary)' }}
              >
                <Activity
                  className="w-12 h-12 mb-3 opacity-50"
                  style={{ color: 'var(--color-text-muted)' }}
                />
                <p style={{ color: 'var(--color-text-muted)' }}>
                  No recent transactions available
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Payment Method Distribution */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <h2
              className="text-xl lg:text-2xl font-semibold"
              style={{ color: 'var(--color-text-primary)' }}
            >
              Payment Analytics
            </h2>
            <div
              className="flex-1 h-px"
              style={{ backgroundColor: 'var(--color-border)' }}
            ></div>
          </div>

          <div
            className="rounded-xl shadow-lg p-6 lg:p-8 border"
            style={{
              backgroundColor: 'var(--color-bg-card)',
              borderColor: 'var(--color-border)'
            }}
          >
          <h2
            className="text-lg lg:text-xl font-semibold mb-4 flex items-center"
            style={{ color: 'var(--color-text-primary)' }}
          >
            <CreditCard className="w-5 h-5 mr-2" style={{ color: 'var(--color-accent)' }} />
            Payment Method Distribution
          </h2>
          {dashboardData.revenueByPaymentMethod && dashboardData.revenueByPaymentMethod.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Enhanced D3 Donut Chart */}
              <div>
                <Suspense fallback={
                  <div className="flex justify-center items-center h-80">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--color-accent)' }}></div>
                  </div>
                }>
                  <D3Chart
                    type="pie"
                    data={dashboardData.revenueByPaymentMethod.map(method => ({
                      label: method.paymentMode || 'Unknown',
                      value: method.totalRevenue,
                      count: method.transactionCount
                    }))}
                    options={{
                      height: 320,
                      xKey: "label",
                      yKey: "value",
                      donut: true,
                      showLegend: true,
                      animate: true,
                      showTooltip: true,
                      responsive: true
                    }}
                  />
                </Suspense>
              </div>
              
              {/* Enhanced Payment Method Cards */}
              <div>
                <h3
                  className="text-md font-medium mb-3"
                  style={{ color: 'var(--color-text-secondary)' }}
                >
                  Payment Details
                </h3>
                <div className="grid grid-cols-1 gap-3">
                  {dashboardData.revenueByPaymentMethod.map((method, index) => (
                    <div
                      key={index}
                      className="rounded-lg p-4 border transition-all duration-200 hover:shadow-md"
                      style={{
                        backgroundColor: 'var(--color-bg-secondary)',
                        borderColor: 'var(--color-border)'
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className="p-2 rounded-md"
                            style={{ backgroundColor: 'var(--color-accent)' }}
                          >
                            <CreditCard className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <h3
                              className="font-medium text-sm"
                              style={{ color: 'var(--color-text-primary)' }}
                            >
                              {method.paymentMode || 'Unknown'}
                            </h3>
                            <p
                              className="text-xs"
                              style={{ color: 'var(--color-text-muted)' }}
                            >
                              {method.transactionCount} transactions
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p
                            className="text-lg font-bold"
                            style={{ color: 'var(--color-accent)' }}
                          >
                            ₹{method.totalRevenue.toLocaleString()}
                          </p>
                          <p
                            className="text-xs"
                            style={{ color: 'var(--color-text-muted)' }}
                          >
                            {((method.totalRevenue / dashboardData.revenueByPaymentMethod.reduce((sum, m) => sum + m.totalRevenue, 0)) * 100).toFixed(1)}%
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div
              className="flex flex-col justify-center items-center h-64 rounded-lg"
              style={{ backgroundColor: 'var(--color-bg-secondary)' }}
            >
              <CreditCard
                className="w-12 h-12 mb-3 opacity-50"
                style={{ color: 'var(--color-text-muted)' }}
              />
              <p style={{ color: 'var(--color-text-muted)' }}>
                No payment data available for the selected period
              </p>
            </div>
          )}
        </div>

        {/* Enhanced Revenue Trend Analysis */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <h2
              className="text-xl lg:text-2xl font-semibold"
              style={{ color: 'var(--color-text-primary)' }}
            >
              Revenue Trends
            </h2>
            <div
              className="flex-1 h-px"
              style={{ backgroundColor: 'var(--color-border)' }}
            ></div>
          </div>

          <div
            className="rounded-xl shadow-lg p-6 lg:p-8 border"
            style={{
              backgroundColor: 'var(--color-bg-card)',
              borderColor: 'var(--color-border)'
            }}
          >
          <h2
            className="text-lg lg:text-xl font-semibold mb-4 flex items-center"
            style={{ color: 'var(--color-text-primary)' }}
          >
            <TrendingUp className="w-5 h-5 mr-2" style={{ color: 'var(--color-accent)' }} />
            Revenue Trend Analysis
          </h2>
          {dashboardData.revenueOverTime && dashboardData.revenueOverTime.length > 0 ? (
            <Suspense fallback={
              <div className="flex justify-center items-center h-80">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--color-accent)' }}></div>
              </div>
            }>
              <D3Chart
                type="area"
                data={dashboardData.revenueOverTime.map(item => ({
                  label: item.date || item.period || 'Unknown',
                  value: item.revenue || 0
                }))}
                options={{
                  height: 350,
                  xKey: "label",
                  yKey: "value",
                  curve: "monotone",
                  showGrid: true,
                  animate: true,
                  showTooltip: true,
                  responsive: true,
                  margin: {
                    top: 20,
                    right: 30,
                    bottom: 50,
                    left: 80
                  }
                }}
              />
            </Suspense>
          ) : (
            <div
              className="flex flex-col justify-center items-center h-64 rounded-lg"
              style={{ backgroundColor: 'var(--color-bg-secondary)' }}
            >
              <TrendingUp
                className="w-12 h-12 mb-3 opacity-50"
                style={{ color: 'var(--color-text-muted)' }}
              />
              <p style={{ color: 'var(--color-text-muted)' }}>
                No revenue trend data available for the selected period
              </p>
            </div>
          )}
        </div>
        </div>
        </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to format duration in hours and minutes
const formatDuration = (minutes) => {
  if (!minutes) return '0h 0m';
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  return `${hours}h ${mins}m`;
};

export default DashboardHome;
