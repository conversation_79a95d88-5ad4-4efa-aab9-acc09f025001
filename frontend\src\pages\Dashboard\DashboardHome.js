import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/authContext';
import { DashboardCard } from '../../components/DashboardCard';
import { Chart } from '../../components/Chart';
import { D3Chart } from '../../components/D3Chart';
import { RecentActivities } from '../../components/RecentActivities';
import { DateRangePicker } from '../../components/DateRangePicker';
import { EntityFilter } from '../../components/EntityFilter';
import { 
  CreditCard, 
  Clock, 
  Car, 
  DollarSign, 
  BarChart2, 
  Activity,
  Filter
} from 'lucide-react';
import dashboardApi from '../../api/dashboardApi';

/**
 * DashboardHome Component
 * 
 * Main dashboard page showing key metrics and visualizations
 * based on user role and permissions
 */
const DashboardHome = () => {
  const { user, isSuperAdmin, isCompanyAdmin, isPlazaManager } = useAuth();
  const [dateRange, setDateRange] = useState('today');
  const [selectedEntity, setSelectedEntity] = useState({});
  const [chartType, setChartType] = useState('bar');
  const [dashboardData, setDashboardData] = useState({
    summary: {},
    revenueByPaymentMethod: [],
    recentTransactions: [],
    peakHours: [],
    loading: true,
    error: null
  });

  // Fetch dashboard data when filters change
  useEffect(() => {
    fetchDashboardData();
  }, [dateRange, selectedEntity]);

  // Function to fetch all dashboard data with progressive loading
  const fetchDashboardData = async () => {
    try {
      setDashboardData(prev => ({ ...prev, loading: true }));
      
      // Initialize an object to store our data as it comes in
      const newData = {};
      
      // Use Promise.all to fetch data in parallel but handle individual failures
      const fetchPromises = [
        // Fetch summary data (highest priority)
        dashboardApi.getDashboardSummary({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.summary = response.data.data;
          // Update state as soon as summary data is available
          setDashboardData(prev => ({ 
            ...prev, 
            summary: response.data.data,
            loading: false 
          }));
        })
        .catch(error => {
          console.error('Error fetching summary data:', error);
          newData.summaryError = 'Failed to load summary data';
        }),
        
        // Fetch recent transactions (medium priority)
        dashboardApi.getRecentTransactions({
          ...selectedEntity,
          limit: 5
        })
        .then(response => {
          newData.recentTransactions = response.data.data;
          // Update state as soon as transaction data is available
          setDashboardData(prev => ({ 
            ...prev, 
            recentTransactions: response.data.data 
          }));
        })
        .catch(error => {
          console.error('Error fetching recent transactions:', error);
          newData.transactionsError = 'Failed to load recent transactions';
        }),
        
        // Fetch revenue by payment method (lower priority)
        dashboardApi.getRevenueByPaymentMethod({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.revenueByPaymentMethod = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({ 
            ...prev, 
            revenueByPaymentMethod: response.data.data 
          }));
        })
        .catch(error => {
          console.error('Error fetching payment method data:', error);
          newData.paymentMethodError = 'Failed to load payment method data';
        }),
        
        // Fetch peak hours data (lowest priority)
        dashboardApi.getPeakHoursData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.peakHours = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({ 
            ...prev, 
            peakHours: response.data.data 
          }));
        })
        .catch(error => {
          console.error('Error fetching peak hours data:', error);
          newData.peakHoursError = 'Failed to load peak hours data';
        })
      ];
      
      // Wait for all promises to settle (either resolve or reject)
      await Promise.allSettled(fetchPromises);
      
      // Final update to ensure all data is in sync and loading state is false
      setDashboardData(prev => ({ 
        ...prev,
        ...newData,
        loading: false,
        // Only set overall error if all requests failed
        error: Object.keys(newData).length === 0 ? 'Failed to load dashboard data' : null
      }));
      
    } catch (error) {
      console.error('Error in dashboard data fetching:', error);
      setDashboardData(prev => ({ 
        ...prev, 
        loading: false, 
        error: 'Failed to load dashboard data' 
      }));
    }
  };

  // Format recent transactions for the activity feed
  const formatRecentActivities = () => {
    if (!dashboardData.recentTransactions) return [];
    
    return dashboardData.recentTransactions.map(transaction => ({
      title: `Vehicle ${transaction.VehicleNumber || 'Unknown'}`,
      time: new Date(transaction.ExitDateTime).toLocaleString(),
      value: `₹${(transaction.ParkingFee + transaction.iTotalGSTFee).toFixed(2)}`,
      color: getPaymentMethodColor(transaction.PaymentMode),
      details: `${transaction.PlazaName} - ${transaction.ExitLane}`
    }));
  };

  // Get color based on payment method
  const getPaymentMethodColor = (method) => {
    switch(method?.toLowerCase()) {
      case 'cash': return 'bg-green-500';
      case 'fastag': return 'bg-blue-500';
      case 'upi': return 'bg-purple-500';
      case 'card': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  // Render loading state
  if (dashboardData.loading) {
    return (
      <div className="p-6 flex justify-center items-center h-64">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-200 rounded-full mb-4"></div>
          <div className="h-4 w-32 bg-blue-200 rounded mb-2"></div>
          <div className="h-3 w-24 bg-blue-100 rounded"></div>
        </div>
      </div>
    );
  }

  // Render error state
  if (dashboardData.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error</p>
          <p>{dashboardData.error}</p>
          <button 
            onClick={fetchDashboardData}
            className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        </div>
        
        {/* Filters Section */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-700 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Filters
            </h2>
            <div className="flex flex-wrap gap-3 items-center">
              <DateRangePicker value={dateRange} onChange={setDateRange} />
              {(isSuperAdmin() || isCompanyAdmin()) && (
                <EntityFilter 
                  userRole={user?.role} 
                  selectedEntity={selectedEntity} 
                  onChange={setSelectedEntity} 
                />
              )}
              {/* Debug user role - hidden */}
              <div style={{ display: 'none' }}>
                User role: {user?.role || 'undefined'}, 
                isSuperAdmin: {isSuperAdmin() ? 'true' : 'false'}, 
                isCompanyAdmin: {isCompanyAdmin() ? 'true' : 'false'}
              </div>
            </div>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <DashboardCard
            title="Total Revenue"
            value={`₹${dashboardData.summary.totalRevenue?.toLocaleString() || '0'}`}
            trend={dashboardData.summary.revenueTrend}
            icon={CreditCard}
            color="bg-blue-500" 
          />
          <DashboardCard
            title="Total Transactions"
            value={dashboardData.summary.transactionCount?.toLocaleString() || '0'}
            trend={dashboardData.summary.transactionTrend}
            icon={Activity}
            color="bg-green-500" 
          />
          <DashboardCard
            title="Vehicles Processed"
            value={dashboardData.summary.vehicleCount?.toLocaleString() || '0'}
            trend={dashboardData.summary.vehicleTrend}
            icon={Car}
            color="bg-purple-500" 
          />
          <DashboardCard
            title="Avg. Parking Duration"
            value={formatDuration(dashboardData.summary.avgDuration)}
            trend={dashboardData.summary.durationTrend}
            icon={Clock}
            color="bg-yellow-500" 
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Transaction Chart */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow p-6">
            {dashboardData.peakHours && dashboardData.peakHours.length > 0 ? (
              <>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-700">Transaction Overview</h2>
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => setChartType('line')}
                      className={`px-3 py-1 text-sm rounded ${chartType === 'line' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                    >
                      Line
                    </button>
                    <button 
                      onClick={() => setChartType('bar')}
                      className={`px-3 py-1 text-sm rounded ${chartType === 'bar' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}
                    >
                      Bar
                    </button>
                  </div>
                </div>
                <D3Chart
                  type={chartType}
                  data={dashboardData.peakHours.map(hour => ({
                    label: `${hour.hour}:00`,
                    value: hour.count
                  }))}
                  options={{
                    height: 350,
                    xKey: "label",
                    yKey: "value",
                    animate: true,
                    showGrid: true,
                    curve: "cardinal",
                    barPadding: 0.2
                  }}
                />
              </>
            ) : (
              <div className="flex justify-center items-center h-64 text-gray-500">
                No transaction data available for the selected period
              </div>
            )}
          </div>

          {/* Recent Activities */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Recent Transactions</h2>
            {dashboardData.recentTransactions && dashboardData.recentTransactions.length > 0 ? (
              <RecentActivities activities={formatRecentActivities()} />
            ) : (
              <div className="flex justify-center items-center h-64 text-gray-500">
                No recent transactions available
              </div>
            )}
          </div>
        </div>

        {/* Payment Method Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-700 mb-4">Payment Method Distribution</h2>
          {dashboardData.revenueByPaymentMethod && dashboardData.revenueByPaymentMethod.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* D3 Pie Chart */}
              <div>
                <D3Chart
                  type="pie"
                  data={dashboardData.revenueByPaymentMethod.map(method => ({
                    label: method.paymentMode || 'Unknown',
                    value: method.totalRevenue,
                    count: method.transactionCount
                  }))}
                  options={{
                    height: 300,
                    xKey: "label",
                    yKey: "value",
                    donut: true,
                    showLegend: true
                  }}
                />
              </div>
              
              {/* Payment Method Cards */}
              <div>
                <h3 className="text-md font-medium text-gray-700 mb-3">Payment Details</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {dashboardData.revenueByPaymentMethod.map((method, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`p-2 rounded-md ${getPaymentMethodColor(method.paymentMode)}`}>
                          <CreditCard className="w-5 h-5 text-white" />
                        </div>
                        <h3 className="font-medium">{method.paymentMode || 'Unknown'}</h3>
                      </div>
                      <p className="text-2xl font-bold">₹{method.totalRevenue.toLocaleString()}</p>
                      <p className="text-sm text-gray-500">{method.transactionCount} transactions</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex justify-center items-center h-32 text-gray-500">
              No payment data available for the selected period
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Helper function to format duration in hours and minutes
const formatDuration = (minutes) => {
  if (!minutes) return '0h 0m';
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  return `${hours}h ${mins}m`;
};

export default DashboardHome;
