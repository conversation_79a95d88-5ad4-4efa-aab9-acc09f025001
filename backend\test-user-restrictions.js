const db = require('./src/config/database');

async function testUserRestrictions() {
  try {
    console.log('=== TESTING USER RESTRICTIONS ===');
    
    // Step 1: Test CompanyAdmin data access
    console.log('\n1. TESTING COMPANY ADMIN DATA ACCESS...');
    
    const companyAdminUser = await db.query(`
      SELECT u.Id, u.Username, r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.Username = 'company admin'
    `);
    
    if (companyAdminUser.recordset.length === 0) {
      console.log('❌ CompanyAdmin user not found');
      return;
    }
    
    const companyAdmin = companyAdminUser.recordset[0];
    console.log(`✓ Found CompanyAdmin: ${companyAdmin.Username} (ID: ${companyAdmin.Id})`);
    
    // Test accessible companies
    const accessibleCompanies = await db.query(`
      SELECT c.Id, c.CompanyName
      FROM Company c
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = ${companyAdmin.Id} AND uc.IsActive = 1 AND c.IsActive = 1
      ORDER BY c.CompanyName
    `);
    
    console.log(`Accessible companies: ${accessibleCompanies.recordset.length}`);
    accessibleCompanies.recordset.forEach(company => {
      console.log(`  ✓ ${company.CompanyName} (ID: ${company.Id})`);
    });
    
    // Test accessible plazas
    const accessiblePlazas = await db.query(`
      SELECT p.Id, p.PlazaName, c.CompanyName
      FROM Plaza p
      JOIN Company c ON p.CompanyId = c.Id
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = ${companyAdmin.Id} AND uc.IsActive = 1 AND p.IsActive = 1
      ORDER BY c.CompanyName, p.PlazaName
    `);
    
    console.log(`Accessible plazas: ${accessiblePlazas.recordset.length}`);
    accessiblePlazas.recordset.forEach(plaza => {
      console.log(`  ✓ ${plaza.PlazaName} (${plaza.CompanyName})`);
    });
    
    // Test accessible users (should exclude SuperAdmin and themselves)
    const accessibleUsers = await db.query(`
      SELECT DISTINCT u.Id, u.Username, u.Email, r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.Id IN (
        -- Users assigned to same companies
        SELECT DISTINCT uc2.UserId
        FROM UserCompany uc1
        JOIN UserCompany uc2 ON uc1.CompanyId = uc2.CompanyId
        WHERE uc1.UserId = ${companyAdmin.Id} AND uc1.IsActive = 1 AND uc2.IsActive = 1
        
        UNION
        
        -- Users assigned to plazas within the companies
        SELECT DISTINCT up.UserId
        FROM UserCompany uc
        JOIN Plaza p ON uc.CompanyId = p.CompanyId
        JOIN UserPlaza up ON p.Id = up.PlazaId
        WHERE uc.UserId = ${companyAdmin.Id} AND uc.IsActive = 1 AND up.IsActive = 1
      )
      AND u.IsActive = 1
      AND r.Name != 'SuperAdmin'  -- Exclude SuperAdmin users
      AND u.Id != ${companyAdmin.Id}  -- Exclude themselves
      ORDER BY r.Name, u.Username
    `);
    
    console.log(`Accessible users (excluding SuperAdmin and self): ${accessibleUsers.recordset.length}`);
    accessibleUsers.recordset.forEach(user => {
      console.log(`  ✓ ${user.Username} (${user.Email}) - ${user.RoleName}`);
    });
    
    // Step 2: Test role filtering
    console.log('\n2. TESTING ROLE FILTERING...');
    
    // Roles that CompanyAdmin should see (excluding SuperAdmin)
    const rolesForCompanyAdmin = await db.query(`
      SELECT * FROM Roles 
      WHERE IsActive = 1 AND Name != 'SuperAdmin'
      ORDER BY Name
    `);
    
    console.log('Roles available to CompanyAdmin:');
    rolesForCompanyAdmin.recordset.forEach(role => {
      console.log(`  ✓ ${role.Name} - ${role.Description}`);
    });
    
    // Roles that PlazaManager should see (excluding SuperAdmin and CompanyAdmin)
    const rolesForPlazaManager = await db.query(`
      SELECT * FROM Roles 
      WHERE IsActive = 1 AND Name NOT IN ('SuperAdmin', 'CompanyAdmin')
      ORDER BY Name
    `);
    
    console.log('\nRoles available to PlazaManager:');
    rolesForPlazaManager.recordset.forEach(role => {
      console.log(`  ✓ ${role.Name} - ${role.Description}`);
    });
    
    // Step 3: Test user creation restrictions
    console.log('\n3. TESTING USER CREATION RESTRICTIONS...');
    
    // Check if SuperAdmin role exists
    const superAdminRole = await db.query(`
      SELECT Id, Name FROM Roles WHERE Name = 'SuperAdmin'
    `);
    
    if (superAdminRole.recordset.length > 0) {
      console.log(`✓ SuperAdmin role found (ID: ${superAdminRole.recordset[0].Id})`);
      console.log('❌ CompanyAdmin should NOT be able to create users with this role');
    }
    
    // Check CompanyAdmin role
    const companyAdminRole = await db.query(`
      SELECT Id, Name FROM Roles WHERE Name = 'CompanyAdmin'
    `);
    
    if (companyAdminRole.recordset.length > 0) {
      console.log(`✓ CompanyAdmin role found (ID: ${companyAdminRole.recordset[0].Id})`);
      console.log('✓ CompanyAdmin should be able to create users with this role');
    }
    
    // Check PlazaManager role
    const plazaManagerRole = await db.query(`
      SELECT Id, Name FROM Roles WHERE Name = 'PlazaManager'
    `);
    
    if (plazaManagerRole.recordset.length > 0) {
      console.log(`✓ PlazaManager role found (ID: ${plazaManagerRole.recordset[0].Id})`);
      console.log('✓ CompanyAdmin should be able to create users with this role');
    }
    
    // Step 4: Test recent user creation by CompanyAdmin
    console.log('\n4. CHECKING RECENT USER CREATIONS...');
    
    const recentCreations = await db.query(`
      SELECT 
        u.Id,
        u.Username,
        u.Email,
        r.Name as RoleName,
        u.CreatedOn,
        creator.Username as CreatedByUsername
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      LEFT JOIN Users creator ON u.CreatedBy = creator.Id
      WHERE u.CreatedBy = ${companyAdmin.Id}
      ORDER BY u.CreatedOn DESC
    `);
    
    console.log(`Users created by CompanyAdmin: ${recentCreations.recordset.length}`);
    recentCreations.recordset.forEach(created => {
      const isProblematic = created.RoleName === 'SuperAdmin' ? '❌ PROBLEM:' : '✓';
      console.log(`  ${isProblematic} ${created.Username} (${created.RoleName}) - ${created.CreatedOn}`);
    });
    
    console.log('\n=== SUMMARY ===');
    console.log('✅ DATA ACCESS WORKING:');
    console.log(`• CompanyAdmin can see ${accessibleCompanies.recordset.length} companies`);
    console.log(`• CompanyAdmin can see ${accessiblePlazas.recordset.length} plazas`);
    console.log(`• CompanyAdmin can see ${accessibleUsers.recordset.length} users (excluding SuperAdmin and self)`);
    
    console.log('\n🔧 BACKEND RESTRICTIONS IMPLEMENTED:');
    console.log('• Role filtering excludes SuperAdmin for CompanyAdmin');
    console.log('• User filtering excludes SuperAdmin users');
    console.log('• User filtering excludes CompanyAdmin themselves');
    console.log('• User creation restrictions prevent SuperAdmin creation');
    console.log('• User editing restrictions prevent self-editing');
    
    console.log('\n📋 READY FOR FRONTEND TESTING:');
    console.log('Login as CompanyAdmin (company admin / PASSWORD) and verify:');
    console.log('1. Companies section shows assigned companies');
    console.log('2. Plaza Management shows plazas from assigned companies');
    console.log('3. User Management shows users (excluding SuperAdmin and self)');
    console.log('4. Add User form does not show SuperAdmin role option');
    console.log('5. Cannot edit own account from user list');
    
    process.exit(0);
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
}

testUserRestrictions();
