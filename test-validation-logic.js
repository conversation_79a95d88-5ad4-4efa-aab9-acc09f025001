// Direct test of validation logic without requiring a running server
// This tests the validation functions directly

function testFieldValidation() {
  console.log('=== TESTING LANE FIELD VALIDATION LOGIC ===\n');
  
  // Simulate the validation logic from LaneController
  function validateLaneFields(data) {
    const validationErrors = [];
    
    const {
      TypeCode, LaneNumber, LaneType, LaneDetails, DataForPrint,
      LaneIP, VehicleType, UpdatedBy, ActiveStatus,
      iDisplayComport, DisplayPole, CashDrawer, MultipleExit,
      Antipassback, HFPasscard, HFPassPort, CoinReaderPort,
      APS_Exit, flgKioskCamera, flgReceiptPrint, flgGKeyDetails,
      flgCKeyCard, flgP4S, flgPasscard, PGTID, pgActivationKey,
      Passcard_Reader_Type, PayTmPG, FlgLPRCamera, LPRCamIP,
      LPRCamID, LPRCamPass, flgPaperSensor, PGSLevel,
      PrinterMake, BarcodeType, PrinterPort, sPaytmWallet,
      sPaytmMID, sPaytmKey, fRecyclerStatus, sSMSKey,
      flgCCUpdateEx, LaneNumber2, VehicleType2, flgSubLane,
      RecyclerType
    } = data;

    // Validate TypeCode (char(2) in database)
    if (TypeCode && TypeCode.length > 2) {
      validationErrors.push({
        field: 'TypeCode',
        error: `TypeCode must be 2 characters or less. Current length: ${TypeCode.length}`,
        maxLength: 2,
        currentValue: TypeCode
      });
    }

    // Validate LaneNumber (char(2) in database)
    if (LaneNumber && LaneNumber.toString().length > 2) {
      validationErrors.push({
        field: 'LaneNumber',
        error: `LaneNumber must be 2 characters or less. Current length: ${LaneNumber.toString().length}`,
        maxLength: 2,
        currentValue: LaneNumber
      });
    }

    // Validate LaneType (char(30) in database)
    if (LaneType && LaneType.length > 30) {
      validationErrors.push({
        field: 'LaneType',
        error: `LaneType must be 30 characters or less. Current length: ${LaneType.length}`,
        maxLength: 30,
        currentValue: LaneType.substring(0, 50) + '...'
      });
    }

    // Validate LaneDetails (char(50) in database)
    if (LaneDetails && LaneDetails.length > 50) {
      validationErrors.push({
        field: 'LaneDetails',
        error: `LaneDetails must be 50 characters or less. Current length: ${LaneDetails.length}`,
        maxLength: 50,
        currentValue: LaneDetails.substring(0, 50) + '...'
      });
    }

    // Additional validations for other char fields
    const charFieldValidations = [
      { field: 'DataForPrint', value: DataForPrint, maxLength: 30 },
      { field: 'LaneIP', value: LaneIP, maxLength: 16 },
      { field: 'VehicleType', value: VehicleType, maxLength: 25 },
      { field: 'UpdatedBy', value: UpdatedBy, maxLength: 10 },
      { field: 'ActiveStatus', value: ActiveStatus, maxLength: 1 },
      { field: 'iDisplayComport', value: iDisplayComport, maxLength: 1 },
      { field: 'DisplayPole', value: DisplayPole, maxLength: 5 },
      { field: 'CashDrawer', value: CashDrawer, maxLength: 5 },
      { field: 'MultipleExit', value: MultipleExit, maxLength: 5 },
      { field: 'Antipassback', value: Antipassback, maxLength: 5 },
      { field: 'HFPasscard', value: HFPasscard, maxLength: 5 },
      { field: 'HFPassPort', value: HFPassPort, maxLength: 2 },
      { field: 'CoinReaderPort', value: CoinReaderPort, maxLength: 2 },
      { field: 'APS_Exit', value: APS_Exit, maxLength: 5 },
      { field: 'flgKioskCamera', value: flgKioskCamera, maxLength: 5 },
      { field: 'flgReceiptPrint', value: flgReceiptPrint, maxLength: 5 },
      { field: 'flgGKeyDetails', value: flgGKeyDetails, maxLength: 5 },
      { field: 'flgCKeyCard', value: flgCKeyCard, maxLength: 5 },
      { field: 'flgP4S', value: flgP4S, maxLength: 5 },
      { field: 'flgPasscard', value: flgPasscard, maxLength: 1 },
      { field: 'PGTID', value: PGTID, maxLength: 8 },
      { field: 'pgActivationKey', value: pgActivationKey, maxLength: 16 },
      { field: 'Passcard_Reader_Type', value: Passcard_Reader_Type, maxLength: 5 },
      { field: 'PayTmPG', value: PayTmPG, maxLength: 5 },
      { field: 'FlgLPRCamera', value: FlgLPRCamera, maxLength: 5 },
      { field: 'LPRCamIP', value: LPRCamIP, maxLength: 15 },
      { field: 'LPRCamID', value: LPRCamID, maxLength: 10 },
      { field: 'LPRCamPass', value: LPRCamPass, maxLength: 10 },
      { field: 'flgPaperSensor', value: flgPaperSensor, maxLength: 1 },
      { field: 'PGSLevel', value: PGSLevel, maxLength: 5 },
      { field: 'PrinterMake', value: PrinterMake, maxLength: 10 },
      { field: 'BarcodeType', value: BarcodeType, maxLength: 2 },
      { field: 'PrinterPort', value: PrinterPort, maxLength: 2 },
      { field: 'sPaytmWallet', value: sPaytmWallet, maxLength: 5 },
      { field: 'sPaytmMID', value: sPaytmMID, maxLength: 50 },
      { field: 'sPaytmKey', value: sPaytmKey, maxLength: 25 },
      { field: 'fRecyclerStatus', value: fRecyclerStatus, maxLength: 5 },
      { field: 'sSMSKey', value: sSMSKey, maxLength: 100 },
      { field: 'flgCCUpdateEx', value: flgCCUpdateEx, maxLength: 1 },
      { field: 'LaneNumber2', value: LaneNumber2, maxLength: 2 },
      { field: 'VehicleType2', value: VehicleType2, maxLength: 25 },
      { field: 'flgSubLane', value: flgSubLane, maxLength: 1 },
      { field: 'RecyclerType', value: RecyclerType, maxLength: 25 }
    ];

    charFieldValidations.forEach(validation => {
      if (validation.value && validation.value.toString().length > validation.maxLength) {
        validationErrors.push({
          field: validation.field,
          error: `${validation.field} must be ${validation.maxLength} characters or less. Current length: ${validation.value.toString().length}`,
          maxLength: validation.maxLength,
          currentValue: validation.value.toString().substring(0, Math.min(50, validation.value.toString().length)) + (validation.value.toString().length > 50 ? '...' : '')
        });
      }
    });

    return validationErrors;
  }

  // Test cases
  const testCases = [
    {
      name: 'Original Problem Case - TypeCode "00" (should pass)',
      data: {
        PlazaID: 13,
        CompanyID: 12,
        LaneNumber: '01',
        LaneType: 'Entry',
        LaneDetails: 'Test Lane',
        TypeCode: '00', // This was the original problem - should now pass
        VehicleType: 'Car',
        UpdatedBy: 'test'
      },
      shouldPass: true
    },
    {
      name: 'TypeCode too long (should fail)',
      data: {
        PlazaID: 13,
        CompanyID: 12,
        LaneNumber: '01',
        LaneType: 'Entry',
        LaneDetails: 'Test Lane',
        TypeCode: '001', // 3 characters - should fail
        VehicleType: 'Car',
        UpdatedBy: 'test'
      },
      shouldPass: false
    },
    {
      name: 'LaneNumber too long (should fail)',
      data: {
        PlazaID: 13,
        CompanyID: 12,
        LaneNumber: '123', // 3 characters - should fail
        LaneType: 'Entry',
        LaneDetails: 'Test Lane',
        TypeCode: '01',
        VehicleType: 'Car',
        UpdatedBy: 'test'
      },
      shouldPass: false
    },
    {
      name: 'LaneType too long (should fail)',
      data: {
        PlazaID: 13,
        CompanyID: 12,
        LaneNumber: '01',
        LaneType: 'This is a very long lane type name that exceeds the thirty character limit', // Too long
        LaneDetails: 'Test Lane',
        TypeCode: '01',
        VehicleType: 'Car',
        UpdatedBy: 'test'
      },
      shouldPass: false
    },
    {
      name: 'Multiple violations (should fail)',
      data: {
        PlazaID: 13,
        CompanyID: 12,
        LaneNumber: '123', // Too long
        LaneType: 'Entry',
        LaneDetails: 'This is a very long lane details description that definitely exceeds the fifty character limit and should cause validation error', // Too long
        TypeCode: '001', // Too long
        VehicleType: 'Car',
        UpdatedBy: 'administrator' // Too long
      },
      shouldPass: false
    },
    {
      name: 'All valid fields (should pass)',
      data: {
        PlazaID: 13,
        CompanyID: 12,
        LaneNumber: '01',
        LaneType: 'Entry',
        LaneDetails: 'Main Entry Lane',
        TypeCode: '01',
        VehicleType: 'Car',
        UpdatedBy: 'admin',
        ActiveStatus: 'Y',
        DisplayPole: 'True',
        PGTID: '12345678',
        pgActivationKey: '1234567890123456'
      },
      shouldPass: true
    }
  ];

  // Run tests
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. Testing: ${testCase.name}`);
    console.log('   Data:', JSON.stringify(testCase.data, null, 2));
    
    const validationErrors = validateLaneFields(testCase.data);
    
    if (testCase.shouldPass) {
      if (validationErrors.length === 0) {
        console.log('   ✅ PASS: No validation errors (as expected)');
      } else {
        console.log('   ❌ FAIL: Expected to pass but got validation errors:');
        validationErrors.forEach(error => {
          console.log(`      - ${error.field}: ${error.error}`);
        });
      }
    } else {
      if (validationErrors.length > 0) {
        console.log('   ✅ PASS: Validation errors caught (as expected):');
        validationErrors.forEach(error => {
          console.log(`      - ${error.field}: ${error.error}`);
          console.log(`        Current: "${error.currentValue}", Max: ${error.maxLength}`);
        });
      } else {
        console.log('   ❌ FAIL: Expected validation errors but none were found');
      }
    }
    
    console.log(''); // Empty line for readability
  });

  // Test the specific original error case
  console.log('=== SPECIFIC TEST FOR ORIGINAL ERROR ===');
  console.log('Testing TypeCode "00" which caused the original truncation error...\n');
  
  const originalErrorData = {
    TypeCode: '00',
    LaneNumber: '01',
    LaneType: 'Entry',
    LaneDetails: 'Test Lane',
    VehicleType: 'Car',
    UpdatedBy: 'test'
  };
  
  const originalValidationErrors = validateLaneFields(originalErrorData);
  
  if (originalValidationErrors.length === 0) {
    console.log('✅ SUCCESS: TypeCode "00" now passes validation!');
    console.log('   The original truncation error has been resolved.');
    console.log('   TypeCode "00" is exactly 2 characters and fits in char(2) column.');
  } else {
    console.log('❌ ISSUE: TypeCode "00" is still being rejected:');
    originalValidationErrors.forEach(error => {
      console.log(`   - ${error.field}: ${error.error}`);
    });
  }
  
  console.log('\n=== VALIDATION LOGIC TEST COMPLETE ===');
  
  // Summary
  const passedTests = testCases.filter((testCase, index) => {
    const validationErrors = validateLaneFields(testCase.data);
    return testCase.shouldPass ? validationErrors.length === 0 : validationErrors.length > 0;
  }).length;
  
  console.log(`\n📊 SUMMARY: ${passedTests}/${testCases.length} tests passed`);
  
  if (passedTests === testCases.length) {
    console.log('🎉 All validation tests passed! The fix is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please review the validation logic.');
  }
}

// Run the test
testFieldValidation();