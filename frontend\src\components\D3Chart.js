// frontend/src/components/D3Chart.js
import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { useTheme } from "../contexts/themeContext";
import "../styles/d3Chart.css";

/**
 * D3Chart Component
 * 
 * A versatile D3.js chart component that can render different chart types:
 * - Line chart with area
 * - Bar chart
 * - Pie/Donut chart
 * 
 * Features:
 * - Interactive tooltips
 * - Animations
 * - Theme support
 * - Responsive design
 */
export function D3Chart({ 
  data,
  type = "line", // "line", "bar", "pie"
  options = {}
}) {
  const svgRef = useRef(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentDataIndex, setCurrentDataIndex] = useState(data?.length || 0);
  const { theme } = useTheme();
  
  // Default options
  const defaultOptions = {
    height: 400,
    margin: { top: 20, right: 30, bottom: 40, left: 60 },
    title: "Chart",
    xKey: "label",
    yKey: "value",
    showLegend: true,
    donut: type === "pie" ? true : false,
    animate: true,
    showTooltip: true,
    showGrid: true,
    curve: "cardinal", // "linear", "cardinal", "step"
    barPadding: 0.2,
    colors: null
  };
  
  // Merge default options with provided options
  const chartOptions = { ...defaultOptions, ...options };
  
  // Define theme-based colors
  const getThemeColors = () => {
    switch(theme) {
      case 'dark':
        return {
          primary: '#3b82f6', // blue-500
          secondary: '#10b981', // emerald-500
          background: '#1f2937', // gray-800
          text: '#f9fafb', // gray-50
          grid: '#4b5563', // gray-600
          tooltip: {
            background: 'rgba(255, 255, 255, 0.9)',
            text: '#1f2937'
          }
        };
      case 'saffron':
        return {
          primary: '#f97316', // orange-500
          secondary: '#eab308', // yellow-500
          background: '#fffbeb', // yellow-50
          text: '#78350f', // yellow-900
          grid: '#d97706', // yellow-600
          tooltip: {
            background: 'rgba(120, 53, 15, 0.9)',
            text: '#fffbeb'
          }
        };
      default: // light
        return {
          primary: '#3b82f6', // blue-500
          secondary: '#10b981', // emerald-500
          background: '#ffffff', // white
          text: '#1f2937', // gray-800
          grid: '#e5e7eb', // gray-200
          tooltip: {
            background: 'rgba(0, 0, 0, 0.8)',
            text: 'white'
          }
        };
    }
  };

  // Generate color scheme based on theme
  const getColorScheme = () => {
    if (chartOptions.colors) return chartOptions.colors;
    
    switch(theme) {
      case 'dark':
        return d3.schemeBlues[9].slice(3);
      case 'saffron':
        return d3.schemeOranges[9].slice(3);
      default:
        return d3.schemeCategory10;
    }
  };

  // Render line chart
  const renderLineChart = (svg, data, colors, width, height) => {
    const { margin, xKey, yKey, curve, showGrid, showTooltip } = chartOptions;
    
    // Create the chart group
    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);
    
    // Create scales
    const xScale = d3.scalePoint()
      .domain(data.map(d => d[xKey]))
      .range([0, width]);
      
    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d[yKey]) * 1.1])
      .range([height, 0]);
    
    // Create line generator
    const line = d3.line()
      .x(d => xScale(d[xKey]))
      .y(d => yScale(d[yKey]))
      .curve(curve === "cardinal" ? d3.curveCardinal : 
             curve === "step" ? d3.curveStep : d3.curveLinear);
    
    // Create area generator
    const area = d3.area()
      .x(d => xScale(d[xKey]))
      .y0(height)
      .y1(d => yScale(d[yKey]))
      .curve(curve === "cardinal" ? d3.curveCardinal : 
             curve === "step" ? d3.curveStep : d3.curveLinear);
    
    // Add gradient definition
    const defs = svg.append("defs");
    const gradient = defs.append("linearGradient")
      .attr("id", "areaGradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0)
      .attr("y1", height)
      .attr("x2", 0)
      .attr("y2", 0);
      
    gradient.append("stop")
      .attr("offset", "0%")
      .attr("stop-color", colors.primary)
      .attr("stop-opacity", 0.1);
      
    gradient.append("stop")
      .attr("offset", "100%")
      .attr("stop-color", colors.primary)
      .attr("stop-opacity", 0.8);
    
    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
        .style("text-anchor", "middle")
        .style("fill", colors.text);
    
    g.append("g")
      .call(d3.axisLeft(yScale)
        .tickFormat(d => {
          if (d >= 1000000) return `₹${(d / 1000000).toFixed(1)}M`;
          if (d >= 1000) return `₹${(d / 1000).toFixed(0)}k`;
          return `₹${d}`;
        }))
      .selectAll("text")
        .style("fill", colors.text);
    
    // Add grid lines
    if (showGrid) {
      g.append("g")
        .attr("class", "grid")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(xScale)
          .tickSize(-height)
          .tickFormat(""))
        .style("stroke-dasharray", "3,3")
        .style("opacity", 0.3);
        
      g.append("g")
        .attr("class", "grid")
        .call(d3.axisLeft(yScale)
          .tickSize(-width)
          .tickFormat(""))
        .style("stroke-dasharray", "3,3")
        .style("opacity", 0.3);
    }
    
    // Current data slice for animation
    const currentData = data.slice(0, currentDataIndex);
    
    // Add the area
    g.append("path")
      .datum(currentData)
      .attr("fill", "url(#areaGradient)")
      .attr("d", area);
    
    // Add the line
    g.append("path")
      .datum(currentData)
      .attr("fill", "none")
      .attr("stroke", colors.primary)
      .attr("stroke-width", 3)
      .attr("d", line);
    
    // Add data points with hover effects
    if (showTooltip) {
      const tooltip = d3.select("body")
        .append("div")
        .attr("class", "d3-tooltip")
        .style("position", "absolute")
        .style("visibility", "hidden")
        .style("background", colors.tooltip.background)
        .style("color", colors.tooltip.text)
        .style("padding", "10px")
        .style("border-radius", "5px")
        .style("font-size", "12px")
        .style("z-index", "1000");
      
      const dots = g.selectAll(".dot")
        .data(currentData)
        .enter()
        .append("circle")
        .attr("class", "dot")
        .attr("cx", d => xScale(d[xKey]))
        .attr("cy", d => yScale(d[yKey]))
        .attr("r", 4)
        .attr("fill", colors.primary)
        .attr("stroke", colors.background)
        .attr("stroke-width", 2)
        .style("cursor", "pointer");
      
      dots.on("mouseover", function(event, d) {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("r", 6);
            
          tooltip.style("visibility", "visible")
            .html(`
              <strong>${d[xKey]}</strong><br/>
              <strong>Value:</strong> ₹${d[yKey].toLocaleString()}<br/>
            `);
        })
        .on("mousemove", (event) => {
          tooltip.style("top", (event.pageY - 10) + "px")
            .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("r", 4);
            
          tooltip.style("visibility", "hidden");
        });
    }
  };

  // Render bar chart
  const renderBarChart = (svg, data, colors, width, height) => {
    const { margin, xKey, yKey, barPadding, showGrid, showTooltip } = chartOptions;
    
    // Create the chart group
    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);
    
    // Create scales
    const xScale = d3.scaleBand()
      .domain(data.map(d => d[xKey]))
      .range([0, width])
      .padding(barPadding);
      
    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d[yKey]) * 1.1])
      .range([height, 0]);
    
    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
        .style("text-anchor", "middle")
        .style("fill", colors.text);
    
    g.append("g")
      .call(d3.axisLeft(yScale)
        .tickFormat(d => {
          if (d >= 1000000) return `₹${(d / 1000000).toFixed(1)}M`;
          if (d >= 1000) return `₹${(d / 1000).toFixed(0)}k`;
          return `₹${d}`;
        }))
      .selectAll("text")
        .style("fill", colors.text);
    
    // Add grid lines
    if (showGrid) {
      g.append("g")
        .attr("class", "grid")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(xScale)
          .tickSize(-height)
          .tickFormat(""))
        .style("stroke-dasharray", "3,3")
        .style("opacity", 0.3);
        
      g.append("g")
        .attr("class", "grid")
        .call(d3.axisLeft(yScale)
          .tickSize(-width)
          .tickFormat(""))
        .style("stroke-dasharray", "3,3")
        .style("opacity", 0.3);
    }
    
    // Current data slice for animation
    const currentData = data.slice(0, currentDataIndex);
    
    // Add the bars
    const bars = g.selectAll(".bar")
      .data(currentData)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", d => xScale(d[xKey]))
      .attr("width", xScale.bandwidth())
      .attr("y", d => yScale(d[yKey]))
      .attr("height", d => height - yScale(d[yKey]))
      .attr("fill", colors.primary)
      .style("cursor", "pointer");
    
    // Add tooltip
    if (showTooltip) {
      const tooltip = d3.select("body")
        .append("div")
        .attr("class", "d3-tooltip")
        .style("position", "absolute")
        .style("visibility", "hidden")
        .style("background", colors.tooltip.background)
        .style("color", colors.tooltip.text)
        .style("padding", "10px")
        .style("border-radius", "5px")
        .style("font-size", "12px")
        .style("z-index", "1000");
      
      bars.on("mouseover", function(event, d) {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("fill", d3.color(colors.primary).darker(0.2));
            
          tooltip.style("visibility", "visible")
            .html(`
              <strong>${d[xKey]}</strong><br/>
              <strong>Value:</strong> ₹${d[yKey].toLocaleString()}<br/>
            `);
        })
        .on("mousemove", (event) => {
          tooltip.style("top", (event.pageY - 10) + "px")
            .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("fill", colors.primary);
            
          tooltip.style("visibility", "hidden");
        });
    }
  };

  // Render pie chart
  const renderPieChart = (svg, data, colors, width, height) => {
    const { margin, xKey, yKey, donut, showTooltip, showLegend } = chartOptions;
    
    // Calculate radius
    const radius = Math.min(width, height) / 2 * 0.8;
    
    // Create the chart group
    const g = svg.append("g")
      .attr("transform", `translate(${width / 2 + margin.left / 2},${height / 2 + margin.top / 2})`);
    
    // Create color scale
    const colorScale = d3.scaleOrdinal()
      .domain(data.map(d => d[xKey]))
      .range(getColorScheme());
    
    // Create pie generator
    const pie = d3.pie()
      .value(d => d[yKey])
      .sort(null);
    
    // Create arc generator
    const arc = d3.arc()
      .innerRadius(donut ? radius * 0.6 : 0)
      .outerRadius(radius);
    
    // Create hover arc generator (slightly larger)
    const hoverArc = d3.arc()
      .innerRadius(donut ? radius * 0.58 : 0)
      .outerRadius(radius * 1.05);
    
    // Add tooltip
    const tooltip = showTooltip ? d3.select("body")
      .append("div")
      .attr("class", "d3-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background", colors.tooltip.background)
      .style("color", colors.tooltip.text)
      .style("padding", "10px")
      .style("border-radius", "5px")
      .style("font-size", "12px")
      .style("z-index", "1000") : null;
    
    // Create pie slices
    const path = g.selectAll("path")
      .data(pie(data))
      .enter()
      .append("path")
      .attr("d", arc)
      .attr("fill", d => colorScale(d.data[xKey]))
      .attr("stroke", colors.background)
      .attr("stroke-width", 2)
      .style("cursor", "pointer")
      .style("transition", "opacity 0.3s");
    
    // Add hover effects
    if (showTooltip) {
      path.on("mouseover", function(event, d) {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("d", hoverArc)
            .attr("opacity", 0.9);
            
          const percent = ((d.endAngle - d.startAngle) / (2 * Math.PI) * 100).toFixed(1);
          
          tooltip.style("visibility", "visible")
            .html(`
              <strong>${d.data[xKey]}</strong><br/>
              <strong>Value:</strong> ₹${d.data[yKey].toLocaleString()}<br/>
              <strong>Percentage:</strong> ${percent}%
            `);
        })
        .on("mousemove", (event) => {
          tooltip.style("top", (event.pageY - 10) + "px")
            .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("d", arc)
            .attr("opacity", 1);
            
          tooltip.style("visibility", "hidden");
        });
    }
    
    // Add center text for donut chart
    if (donut) {
      const total = d3.sum(data, d => d[yKey]);
      
      g.append("text")
        .attr("text-anchor", "middle")
        .attr("dy", "-0.2em")
        .style("font-size", "16px")
        .style("font-weight", "bold")
        .style("fill", colors.text)
        .text("Total");
        
      g.append("text")
        .attr("text-anchor", "middle")
        .attr("dy", "1em")
        .style("font-size", "16px")
        .style("fill", colors.text)
        .text(`₹${total.toLocaleString()}`);
    }
    
    // Add legend if requested
    if (showLegend) {
      const legendG = svg.append("g")
        .attr("transform", `translate(${margin.left},${height + margin.top + 20})`);
      
      const legendItems = legendG.selectAll("g")
        .data(data)
        .enter()
        .append("g")
        .attr("transform", (d, i) => `translate(${i * 100}, 0)`);
      
      legendItems.append("rect")
        .attr("width", 15)
        .attr("height", 15)
        .attr("fill", d => colorScale(d[xKey]));
      
      legendItems.append("text")
        .attr("x", 20)
        .attr("y", 12)
        .style("font-size", "12px")
        .style("fill", colors.text)
        .text(d => d[xKey]);
    }
  };

  useEffect(() => {
    if (!svgRef.current || !data || data.length === 0) return;
    
    const colors = getThemeColors();
    
    // Clear previous chart
    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();
    
    // Set dimensions
    const { margin, height: chartHeight, showLegend } = chartOptions;
    const width = svgRef.current.clientWidth - margin.left - margin.right;
    const height = chartHeight - margin.top - margin.bottom;
    
    // Render the appropriate chart type
    switch(type) {
      case "bar":
        renderBarChart(svg, data, colors, width, height);
        break;
      case "pie":
        renderPieChart(svg, data, colors, width, height);
        break;
      case "line":
      default:
        renderLineChart(svg, data, colors, width, height);
        break;
    }
    
    // Cleanup tooltip on unmount
    return () => {
      d3.select(".d3-tooltip").remove();
    };
  }, [data, type, chartOptions, currentDataIndex, theme]);

  // Animation functions
  const animateChart = () => {
    if (isAnimating || !data) return;
    
    setIsAnimating(true);
    setCurrentDataIndex(1);
    
    const interval = setInterval(() => {
      setCurrentDataIndex(prev => {
        if (prev >= data.length) {
          clearInterval(interval);
          setIsAnimating(false);
          return data.length;
        }
        return prev + 1;
      });
    }, 100);
  };

  const resetChart = () => {
    if (!data) return;
    setCurrentDataIndex(data.length);
    setIsAnimating(false);
  };

  // If no data, show a placeholder
  if (!data || data.length === 0) {
    return (
      <div className="flex justify-center items-center h-64 text-gray-500 dark:text-gray-400">
        No data available for the selected period
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">{chartOptions.title}</h2>
        {chartOptions.animate && (
          <div className="flex items-center space-x-2">
            <button 
              className="px-3 py-1 text-sm border rounded-md flex items-center gap-1 hover:bg-gray-100 dark:hover:bg-gray-700 dark:border-gray-600"
              onClick={animateChart} 
              disabled={isAnimating}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
              Animate
            </button>
            <button 
              className="px-3 py-1 text-sm border rounded-md flex items-center gap-1 hover:bg-gray-100 dark:hover:bg-gray-700 dark:border-gray-600"
              onClick={resetChart}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 2v6h6"></path>
                <path d="M21 12A9 9 0 0 0 6 5.3L3 8"></path>
                <path d="M21 22v-6h-6"></path>
                <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"></path>
              </svg>
              Reset
            </button>
          </div>
        )}
      </div>
      <div className="w-full overflow-x-auto">
        <svg 
          ref={svgRef} 
          width="100%" 
          height={chartOptions.height + (chartOptions.showLegend ? 50 : 0)} 
          className="w-full" 
          style={{ minWidth: type === "pie" ? "300px" : "500px" }}
        />
      </div>
    </div>
  );
}