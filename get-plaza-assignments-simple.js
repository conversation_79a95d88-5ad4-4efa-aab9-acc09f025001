// Node.js script to get all plazas assigned to users
// This can be called from PowerShell: node get-plaza-assignments-simple.js

const db = require('./backend/src/config/database');

async function getPlazaAssignments() {
  try {
    console.log('=== PLAZA ASSIGNMENTS REPORT ===');
    console.log('Connecting to database: ParkwizOps\n');
    
    // First, check UserPlaza table structure
    console.log('Checking UserPlaza table structure...');
    const userPlazaStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'UserPlaza'
      ORDER BY ORDINAL_POSITION
    `);
    
    if (userPlazaStructure.recordset.length > 0) {
      console.log('UserPlaza table structure:');
      userPlazaStructure.recordset.forEach(col => {
        console.log(`  ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
      });
    } else {
      console.log('UserPlaza table not found or empty');
    }
    
    // Main query to get all plaza assignments
    console.log('\nExecuting main query for plaza assignments...');
    const mainQuery = `
      SELECT 
        u.Id as UserId,
        u.Username,
        u.FirstName,
        u.LastName,
        u.Email,
        r.Name as RoleName,
        p.Id as PlazaId,
        p.PlazaName,
        p.PlazaCode,
        p.ContactPerson as PlazaContactPerson,
        p.ContactNumber as PlazaContactNumber,
        c.CompanyName,
        up.IsActive as AssignmentActive,
        up.CreatedOn as AssignedOn
      FROM Users u
      INNER JOIN UserPlaza up ON u.Id = up.UserId
      INNER JOIN Plaza p ON up.PlazaId = p.Id
      INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      LEFT JOIN Roles r ON u.RoleId = r.Id
      WHERE u.IsActive = 1 AND p.IsActive = 1
      ORDER BY u.Username, p.PlazaName
    `;
    
    const results = await db.query(mainQuery);
    
    if (results.recordset.length > 0) {
      console.log('\nPLAZA ASSIGNMENTS FOUND:');
      console.log(`Total assignments: ${results.recordset.length}\n`);
      
      // Group by user for better readability
      const userGroups = {};
      results.recordset.forEach(row => {
        if (!userGroups[row.Username]) {
          userGroups[row.Username] = [];
        }
        userGroups[row.Username].push(row);
      });
      
      Object.keys(userGroups).forEach(username => {
        const userAssignments = userGroups[username];
        const user = userAssignments[0];
        
        console.log(`--- USER: ${user.Username} (${user.FirstName} ${user.LastName}) ---`);
        console.log(`Role: ${user.RoleName}`);
        console.log(`Email: ${user.Email}`);
        console.log('Assigned Plazas:');
        
        userAssignments.forEach(assignment => {
          console.log(`  • ${assignment.PlazaName} (Code: ${assignment.PlazaCode})`);
          console.log(`    Company: ${assignment.CompanyName}`);
          console.log(`    Contact: ${assignment.PlazaContactPerson} - ${assignment.PlazaContactNumber}`);
          console.log(`    Assigned On: ${assignment.AssignedOn}`);
          console.log('');
        });
      });
      
      // Summary by role
      console.log('=== SUMMARY BY ROLE ===');
      const roleSummary = {};
      results.recordset.forEach(row => {
        if (!roleSummary[row.RoleName]) {
          roleSummary[row.RoleName] = { users: new Set(), assignments: 0 };
        }
        roleSummary[row.RoleName].users.add(row.UserId);
        roleSummary[row.RoleName].assignments++;
      });
      
      Object.keys(roleSummary).forEach(role => {
        const summary = roleSummary[role];
        console.log(`${role}: ${summary.users.size} users with ${summary.assignments} total plaza assignments`);
      });
      
    } else {
      console.log('No plaza assignments found.');
      
      // Check if there are any users and plazas at all
      console.log('\nChecking for users...');
      const userCount = await db.query('SELECT COUNT(*) as UserCount FROM Users WHERE IsActive = 1');
      console.log(`Active users: ${userCount.recordset[0].UserCount}`);
      
      console.log('Checking for plazas...');
      const plazaCount = await db.query('SELECT COUNT(*) as PlazaCount FROM Plaza WHERE IsActive = 1');
      console.log(`Active plazas: ${plazaCount.recordset[0].PlazaCount}`);
      
      console.log('Checking UserPlaza table...');
      const userPlazaCount = await db.query('SELECT COUNT(*) as AssignmentCount FROM UserPlaza');
      console.log(`Total UserPlaza records: ${userPlazaCount.recordset[0].AssignmentCount}`);
    }
    
    // Additional query to show users without plaza assignments
    console.log('\n=== USERS WITHOUT PLAZA ASSIGNMENTS ===');
    const usersWithoutPlazasQuery = `
      SELECT 
        u.Id,
        u.Username,
        u.FirstName,
        u.LastName,
        u.Email,
        r.Name as RoleName
      FROM Users u
      LEFT JOIN UserPlaza up ON u.Id = up.UserId
      LEFT JOIN Roles r ON u.RoleId = r.Id
      WHERE u.IsActive = 1 AND up.UserId IS NULL
      ORDER BY u.Username
    `;
    
    const usersWithoutPlazas = await db.query(usersWithoutPlazasQuery);
    
    if (usersWithoutPlazas.recordset.length > 0) {
      console.log(`Found ${usersWithoutPlazas.recordset.length} users without plaza assignments:`);
      usersWithoutPlazas.recordset.forEach(user => {
        console.log(`  • ${user.Username} (${user.FirstName} ${user.LastName}) - ${user.RoleName} - ${user.Email}`);
      });
    } else {
      console.log('All active users have plaza assignments.');
    }
    
    console.log('\n=== REPORT COMPLETE ===');
    process.exit(0);
    
  } catch (error) {
    console.error('Error executing query:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

getPlazaAssignments();