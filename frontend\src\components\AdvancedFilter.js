import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/authContext';
import { 
  Filter, 
  Building2, 
  MapPin, 
  Calendar,
  ChevronDown,
  X,
  Search
} from 'lucide-react';
import { companyApi } from '../api/companyApi';
import { plazaApi } from '../api/plazaApi';

/**
 * AdvancedFilter Component
 * 
 * A comprehensive filtering system for dashboard data with hierarchical filtering
 * Supports filtering by:
 * - Date range (with custom date picker)
 * - Company (All companies, specific company)
 * - Plaza (All plazas in company, specific plaza)
 * - Additional filters (payment method, vehicle type, etc.)
 */
export function AdvancedFilter({ filters, onChange, className = "" }) {
  const { isSuperAdmin, isCompanyAdmin } = useAuth();
  const [isExpanded, setIsExpanded] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [plazas, setPlazas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Initialize filters with defaults
  const currentFilters = {
    dateRange: 'today',
    companyId: '',
    plazaId: '',
    paymentMethod: '',
    vehicleType: '',
    customStartDate: '',
    customEndDate: '',
    ...filters
  };

  // Date range options
  const dateRangeOptions = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'quarter', label: 'This Quarter' },
    { value: 'year', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  // Payment method options
  const paymentMethodOptions = [
    { value: '', label: 'All Payment Methods' },
    { value: 'cash', label: 'Cash' },
    { value: 'card', label: 'Card' },
    { value: 'upi', label: 'UPI' },
    { value: 'fastag', label: 'FASTag' },
    { value: 'wallet', label: 'Digital Wallet' }
  ];

  // Vehicle type options
  const vehicleTypeOptions = [
    { value: '', label: 'All Vehicle Types' },
    { value: 'car', label: 'Car' },
    { value: 'bike', label: 'Motorcycle' },
    { value: 'truck', label: 'Truck' },
    { value: 'bus', label: 'Bus' },
    { value: 'auto', label: 'Auto Rickshaw' }
  ];

  // Fetch companies on component mount
  useEffect(() => {
    const fetchCompanies = async () => {
      if (!isSuperAdmin()) return;
      
      try {
        setLoading(true);
        const response = await companyApi.getCompanies();
        setCompanies(response.data.data || []);
      } catch (error) {
        console.error('Error fetching companies:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompanies();
  }, [isSuperAdmin]);

  // Fetch plazas when company changes
  useEffect(() => {
    const fetchPlazas = async () => {
      try {
        setLoading(true);
        let plazasData = [];

        if (isSuperAdmin() && currentFilters.companyId) {
          // SuperAdmin: fetch plazas for selected company
          const response = await plazaApi.getPlazasByCompany(currentFilters.companyId);
          plazasData = response.data.data || [];
        } else if (isCompanyAdmin()) {
          // CompanyAdmin: fetch plazas for their company
          const response = await plazaApi.getPlazas();
          plazasData = response.data.data || [];
        }

        setPlazas(plazasData);
      } catch (error) {
        console.error('Error fetching plazas:', error);
        setPlazas([]);
      } finally {
        setLoading(false);
      }
    };

    if (isSuperAdmin() || isCompanyAdmin()) {
      fetchPlazas();
    }
  }, [currentFilters.companyId, isSuperAdmin, isCompanyAdmin]);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    const newFilters = { ...currentFilters, [key]: value };
    
    // Reset dependent filters
    if (key === 'companyId') {
      newFilters.plazaId = '';
    }
    
    onChange(newFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    const clearedFilters = {
      dateRange: 'today',
      companyId: '',
      plazaId: '',
      paymentMethod: '',
      vehicleType: '',
      customStartDate: '',
      customEndDate: ''
    };
    onChange(clearedFilters);
  };

  // Filter companies and plazas based on search
  const filteredCompanies = companies.filter(company =>
    company.CompanyName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredPlazas = plazas.filter(plaza =>
    plaza.PlazaName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    plaza.Name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (currentFilters.companyId) count++;
    if (currentFilters.plazaId) count++;
    if (currentFilters.paymentMethod) count++;
    if (currentFilters.vehicleType) count++;
    if (currentFilters.dateRange === 'custom') count++;
    return count;
  };

  return (
    <div 
      className={`rounded-lg shadow-lg p-4 lg:p-6 ${className}`}
      style={{ backgroundColor: 'var(--color-bg-card)' }}
    >
      {/* Filter Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3">
        <div className="flex items-center">
          <Filter 
            className="w-5 h-5 mr-2" 
            style={{ color: 'var(--color-accent)' }} 
          />
          <h2 
            className="text-lg font-semibold"
            style={{ color: 'var(--color-text-primary)' }}
          >
            Advanced Filters
          </h2>
          {getActiveFilterCount() > 0 && (
            <span 
              className="ml-2 px-2 py-1 text-xs rounded-full"
              style={{ 
                backgroundColor: 'var(--color-accent)',
                color: 'white'
              }}
            >
              {getActiveFilterCount()}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center px-3 py-2 text-sm rounded-lg transition-all duration-200"
            style={{
              backgroundColor: 'var(--color-bg-button)',
              color: 'var(--color-text-primary)',
              border: '1px solid var(--color-border)'
            }}
          >
            {isExpanded ? 'Collapse' : 'Expand'}
            <ChevronDown 
              className={`w-4 h-4 ml-1 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
            />
          </button>
          
          {getActiveFilterCount() > 0 && (
            <button
              onClick={clearFilters}
              className="flex items-center px-3 py-2 text-sm rounded-lg transition-all duration-200 hover:bg-red-50"
              style={{
                color: 'var(--color-error)',
                border: '1px solid var(--color-error)'
              }}
            >
              <X className="w-4 h-4 mr-1" />
              Clear
            </button>
          )}
        </div>
      </div>

      {/* Basic Filters - Always Visible */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {/* Date Range Filter */}
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: 'var(--color-text-secondary)' }}
          >
            <Calendar className="w-4 h-4 inline mr-1" />
            Date Range
          </label>
          <select
            value={currentFilters.dateRange}
            onChange={(e) => handleFilterChange('dateRange', e.target.value)}
            className="w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 transition-all"
            style={{
              backgroundColor: 'var(--color-bg-input)',
              borderColor: 'var(--color-border)',
              color: 'var(--color-text-primary)'
            }}
          >
            {dateRangeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Company Filter - Only for SuperAdmin */}
        {isSuperAdmin() && (
          <div>
            <label 
              className="block text-sm font-medium mb-2"
              style={{ color: 'var(--color-text-secondary)' }}
            >
              <Building2 className="w-4 h-4 inline mr-1" />
              Company
            </label>
            <select
              value={currentFilters.companyId}
              onChange={(e) => handleFilterChange('companyId', e.target.value)}
              className="w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 transition-all"
              style={{
                backgroundColor: 'var(--color-bg-input)',
                borderColor: 'var(--color-border)',
                color: 'var(--color-text-primary)'
              }}
              disabled={loading}
            >
              <option value="">All Companies</option>
              {filteredCompanies.map(company => (
                <option key={company.Id} value={company.Id}>
                  {company.CompanyName}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Plaza Filter - For SuperAdmin and CompanyAdmin */}
        {(isSuperAdmin() || isCompanyAdmin()) && (
          <div>
            <label 
              className="block text-sm font-medium mb-2"
              style={{ color: 'var(--color-text-secondary)' }}
            >
              <MapPin className="w-4 h-4 inline mr-1" />
              Plaza
            </label>
            <select
              value={currentFilters.plazaId}
              onChange={(e) => handleFilterChange('plazaId', e.target.value)}
              className="w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 transition-all"
              style={{
                backgroundColor: 'var(--color-bg-input)',
                borderColor: 'var(--color-border)',
                color: 'var(--color-text-primary)'
              }}
              disabled={loading || (isSuperAdmin() && !currentFilters.companyId)}
            >
              <option value="">All Plazas</option>
              {filteredPlazas.map(plaza => (
                <option key={plaza.Id} value={plaza.Id}>
                  {plaza.PlazaName || plaza.Name || `Plaza ${plaza.Id}`}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Custom Date Range - Show when custom is selected */}
      {currentFilters.dateRange === 'custom' && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4 p-4 rounded-lg" style={{ backgroundColor: 'var(--color-bg-secondary)' }}>
          <div>
            <label 
              className="block text-sm font-medium mb-2"
              style={{ color: 'var(--color-text-secondary)' }}
            >
              Start Date
            </label>
            <input
              type="date"
              value={currentFilters.customStartDate}
              onChange={(e) => handleFilterChange('customStartDate', e.target.value)}
              className="w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 transition-all"
              style={{
                backgroundColor: 'var(--color-bg-input)',
                borderColor: 'var(--color-border)',
                color: 'var(--color-text-primary)'
              }}
            />
          </div>
          <div>
            <label 
              className="block text-sm font-medium mb-2"
              style={{ color: 'var(--color-text-secondary)' }}
            >
              End Date
            </label>
            <input
              type="date"
              value={currentFilters.customEndDate}
              onChange={(e) => handleFilterChange('customEndDate', e.target.value)}
              className="w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 transition-all"
              style={{
                backgroundColor: 'var(--color-bg-input)',
                borderColor: 'var(--color-border)',
                color: 'var(--color-text-primary)'
              }}
            />
          </div>
        </div>
      )}

      {/* Advanced Filters - Show when expanded */}
      {isExpanded && (
        <div className="border-t pt-4" style={{ borderColor: 'var(--color-border)' }}>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            {/* Payment Method Filter */}
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--color-text-secondary)' }}
              >
                Payment Method
              </label>
              <select
                value={currentFilters.paymentMethod}
                onChange={(e) => handleFilterChange('paymentMethod', e.target.value)}
                className="w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 transition-all"
                style={{
                  backgroundColor: 'var(--color-bg-input)',
                  borderColor: 'var(--color-border)',
                  color: 'var(--color-text-primary)'
                }}
              >
                {paymentMethodOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Vehicle Type Filter */}
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--color-text-secondary)' }}
              >
                Vehicle Type
              </label>
              <select
                value={currentFilters.vehicleType}
                onChange={(e) => handleFilterChange('vehicleType', e.target.value)}
                className="w-full px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 transition-all"
                style={{
                  backgroundColor: 'var(--color-bg-input)',
                  borderColor: 'var(--color-border)',
                  color: 'var(--color-text-primary)'
                }}
              >
                {vehicleTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Search Filter */}
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--color-text-secondary)' }}
              >
                Search
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" style={{ color: 'var(--color-text-muted)' }} />
                <input
                  type="text"
                  placeholder="Search companies, plazas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 rounded-lg border focus:outline-none focus:ring-2 transition-all"
                  style={{
                    backgroundColor: 'var(--color-bg-input)',
                    borderColor: 'var(--color-border)',
                    color: 'var(--color-text-primary)'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
