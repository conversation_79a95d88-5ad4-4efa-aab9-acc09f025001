<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast Notification System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .toast-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .toast-card {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
            background: #f9fafb;
        }
        .btn {
            width: 100%;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            margin-top: 10px;
        }
        .btn-success {
            background: #10b981;
            color: white;
        }
        .btn-success:hover {
            background: #059669;
        }
        .btn-error {
            background: #ef4444;
            color: white;
        }
        .btn-error:hover {
            background: #dc2626;
        }
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        .btn-warning:hover {
            background: #d97706;
        }
        .btn-info {
            background: #3b82f6;
            color: white;
        }
        .btn-info:hover {
            background: #2563eb;
        }
        .toast-preview {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
            width: 100%;
        }
        .toast-item {
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            margin-bottom: 10px;
            overflow: hidden;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease-in-out;
        }
        .toast-item.show {
            transform: translateX(0);
            opacity: 1;
        }
        .toast-item.hide {
            transform: translateX(100%);
            opacity: 0;
        }
        .toast-content {
            padding: 16px;
            display: flex;
            align-items: flex-start;
        }
        .toast-icon {
            margin-right: 12px;
            font-size: 20px;
        }
        .toast-message {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }
        .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #6b7280;
            padding: 0;
            margin-left: 12px;
        }
        .toast-close:hover {
            color: #374151;
        }
        .toast-success {
            border-left: 4px solid #10b981;
        }
        .toast-success .toast-icon {
            color: #10b981;
        }
        .toast-error {
            border-left: 4px solid #ef4444;
        }
        .toast-error .toast-icon {
            color: #ef4444;
        }
        .toast-warning {
            border-left: 4px solid #f59e0b;
        }
        .toast-warning .toast-icon {
            color: #f59e0b;
        }
        .toast-info {
            border-left: 4px solid #3b82f6;
        }
        .toast-info .toast-icon {
            color: #3b82f6;
        }
        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            color: #1e40af;
            margin-top: 0;
        }
        .crud-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .crud-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }
        .crud-card h4 {
            margin-top: 0;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>🍞 Toast Notification System Test</h1>
    
    <div class="instructions">
        <h3>📋 Toast Notification Features</h3>
        <p><strong>Features Implemented:</strong></p>
        <ul>
            <li>✅ <strong>4 Toast Types:</strong> Success, Error, Warning, Info</li>
            <li>✅ <strong>Auto-dismiss:</strong> Configurable duration (default 5s)</li>
            <li>✅ <strong>Manual dismiss:</strong> Click X button to close</li>
            <li>✅ <strong>Stacking:</strong> Multiple toasts stack vertically</li>
            <li>✅ <strong>Animations:</strong> Slide-in from right, fade-out</li>
            <li>✅ <strong>CRUD Helpers:</strong> Pre-built messages for common operations</li>
        </ul>
        <p><strong>Test Instructions:</strong> Click the buttons below to see different toast types</p>
    </div>

    <div class="test-section">
        <h2 class="test-header">🎨 Basic Toast Types</h2>
        <div class="toast-demo">
            <div class="toast-card">
                <h4>✅ Success Toast</h4>
                <p>Used for successful operations</p>
                <button class="btn btn-success" onclick="showToast('success', '✅ Operation completed successfully!')">
                    Show Success Toast
                </button>
            </div>
            
            <div class="toast-card">
                <h4>❌ Error Toast</h4>
                <p>Used for failed operations</p>
                <button class="btn btn-error" onclick="showToast('error', '❌ Operation failed. Please try again.')">
                    Show Error Toast
                </button>
            </div>
            
            <div class="toast-card">
                <h4>⚠️ Warning Toast</h4>
                <p>Used for unauthorized actions</p>
                <button class="btn btn-warning" onclick="showToast('warning', '⚠️ You are not authorized to perform this action')">
                    Show Warning Toast
                </button>
            </div>
            
            <div class="toast-card">
                <h4>ℹ️ Info Toast</h4>
                <p>Used for general information</p>
                <button class="btn btn-info" onclick="showToast('info', 'ℹ️ Data has been refreshed')">
                    Show Info Toast
                </button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-header">🔧 CRUD Operation Toasts</h2>
        <div class="crud-examples">
            <div class="crud-card">
                <h4>👥 User Management</h4>
                <button class="btn btn-success" onclick="showCrudToast('success', 'create', 'User')">Create User Success</button>
                <button class="btn btn-success" onclick="showCrudToast('success', 'update', 'User')">Update User Success</button>
                <button class="btn btn-success" onclick="showCrudToast('success', 'delete', 'User')">Delete User Success</button>
                <button class="btn btn-error" onclick="showCrudToast('error', 'create', 'User')">Create User Error</button>
            </div>
            
            <div class="crud-card">
                <h4>🏢 Company Management</h4>
                <button class="btn btn-success" onclick="showCrudToast('success', 'create', 'Company')">Create Company Success</button>
                <button class="btn btn-success" onclick="showCrudToast('success', 'update', 'Company')">Update Company Success</button>
                <button class="btn btn-success" onclick="showCrudToast('success', 'delete', 'Company')">Delete Company Success</button>
                <button class="btn btn-error" onclick="showCrudToast('error', 'update', 'Company')">Update Company Error</button>
            </div>
            
            <div class="crud-card">
                <h4>🏪 Plaza Management</h4>
                <button class="btn btn-success" onclick="showCrudToast('success', 'create', 'Plaza')">Create Plaza Success</button>
                <button class="btn btn-success" onclick="showCrudToast('success', 'toggle', 'Plaza')">Toggle Plaza Status</button>
                <button class="btn btn-error" onclick="showCrudToast('error', 'delete', 'Plaza')">Delete Plaza Error</button>
                <button class="btn btn-warning" onclick="showUnauthorizedToast('delete', 'plazas')">Unauthorized Delete</button>
            </div>
            
            <div class="crud-card">
                <h4>🛣️ Lane Management</h4>
                <button class="btn btn-success" onclick="showCrudToast('success', 'create', 'Lane')">Create Lane Success</button>
                <button class="btn btn-success" onclick="showCrudToast('success', 'toggle', 'Lane')">Toggle Lane Status</button>
                <button class="btn btn-error" onclick="showCrudToast('error', 'create', 'Lane')">Create Lane Error</button>
                <button class="btn btn-warning" onclick="showUnauthorizedToast('edit', 'lanes')">Unauthorized Edit</button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-header">🧪 Advanced Features</h2>
        <div class="toast-demo">
            <div class="toast-card">
                <h4>⏱️ Custom Duration</h4>
                <p>Toast stays for 10 seconds</p>
                <button class="btn btn-info" onclick="showCustomToast()">
                    Show Long Duration Toast
                </button>
            </div>
            
            <div class="toast-card">
                <h4>📚 Multiple Toasts</h4>
                <p>Show multiple toasts at once</p>
                <button class="btn btn-success" onclick="showMultipleToasts()">
                    Show Multiple Toasts
                </button>
            </div>
            
            <div class="toast-card">
                <h4>🌐 Network Error</h4>
                <p>Common network error message</p>
                <button class="btn btn-error" onclick="showNetworkError()">
                    Show Network Error
                </button>
            </div>
            
            <div class="toast-card">
                <h4>🧹 Clear All</h4>
                <p>Remove all active toasts</p>
                <button class="btn btn-warning" onclick="clearAllToasts()">
                    Clear All Toasts
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-preview" id="toastContainer"></div>

    <script>
        let toastId = 0;
        const toasts = new Map();

        function generateId() {
            return ++toastId;
        }

        function getToastIcon(type) {
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            return icons[type] || icons.info;
        }

        function createToastElement(type, message, duration = 5000) {
            const id = generateId();
            const toast = document.createElement('div');
            toast.className = `toast-item toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <div class="toast-icon">${getToastIcon(type)}</div>
                    <div class="toast-message">${message}</div>
                    <button class="toast-close" onclick="removeToast(${id})">×</button>
                </div>
            `;

            const container = document.getElementById('toastContainer');
            container.appendChild(toast);

            // Trigger show animation
            setTimeout(() => toast.classList.add('show'), 10);

            // Auto-remove after duration
            if (duration > 0) {
                setTimeout(() => removeToast(id), duration);
            }

            toasts.set(id, toast);
            return id;
        }

        function removeToast(id) {
            const toast = toasts.get(id);
            if (toast) {
                toast.classList.add('hide');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                    toasts.delete(id);
                }, 300);
            }
        }

        function showToast(type, message, duration = 5000) {
            return createToastElement(type, message, duration);
        }

        function showCrudToast(result, action, entity) {
            if (result === 'success') {
                const messages = {
                    create: `✅ ${entity} created successfully`,
                    update: `✅ ${entity} updated successfully`,
                    delete: `✅ ${entity} deleted successfully`,
                    toggle: `✅ ${entity} status updated successfully`
                };
                showToast('success', messages[action]);
            } else {
                const messages = {
                    create: `❌ Failed to create ${entity}`,
                    update: `❌ Failed to update ${entity}`,
                    delete: `❌ Failed to delete ${entity}`,
                    toggle: `❌ Failed to update ${entity} status`
                };
                showToast('error', messages[action]);
            }
        }

        function showUnauthorizedToast(action, module) {
            const message = `⚠️ You are not authorized to ${action} ${module}`;
            showToast('warning', message);
        }

        function showCustomToast() {
            showToast('info', 'ℹ️ This toast will stay for 10 seconds', 10000);
        }

        function showMultipleToasts() {
            showToast('success', '✅ First operation completed');
            setTimeout(() => showToast('info', 'ℹ️ Processing second operation'), 500);
            setTimeout(() => showToast('success', '✅ Second operation completed'), 1000);
            setTimeout(() => showToast('warning', '⚠️ Third operation needs attention'), 1500);
        }

        function showNetworkError() {
            showToast('error', '🌐 Network error. Please check your connection and try again.', 7000);
        }

        function clearAllToasts() {
            toasts.forEach((toast, id) => {
                removeToast(id);
            });
        }
    </script>
</body>
</html>
