# PWVMS Deployment Handoff Document

## Overview

This document provides comprehensive instructions for deploying the PWVMS (Parking and Toll Management System) application on a Windows server with IIS. The deployment package contains a production-ready build that requires no additional compilation or build steps.

## Deployment Package Contents

The deployment package (`PWVMS-Deployment-[timestamp].zip`) contains:

```
PWVMS-Deployment/
├── backend/                 # Node.js backend with all dependencies
│   ├── node_modules/        # Pre-installed production dependencies
│   ├── src/                 # Backend source code
│   ├── Uploads/             # Upload directories structure
│   ├── package.json         # Node.js package configuration
│   └── .env.template        # Environment configuration template
├── frontend/
│   └── build/               # Pre-built React frontend (production-ready)
├── docs/                    # Application documentation
├── web.config               # IIS configuration file
├── deploy-to-iis.ps1        # IIS deployment script
├── install-iisnode.ps1      # Prerequisites installation script
├── DEPLOYMENT-GUIDE.md      # Quick deployment guide
└── README.md                # General information
```

## System Requirements

- **Operating System**: Windows Server 2016/2019/2022 or Windows 10/11
- **Web Server**: Internet Information Services (IIS) 10.0 or later
- **Runtime**: Node.js 14.x LTS or later
- **Database**: Microsoft SQL Server (Azure SQL Database or on-premises)
- **RAM**: 4GB minimum (8GB recommended)
- **Disk Space**: 10GB free space minimum
- **Network**: Outbound internet access for npm (during installation only)

## Required IIS Components

- URL Rewrite Module
- Application Request Routing (ARR)
- iisnode v0.2.26 (specific version required)

## Deployment Instructions

### Step 1: Prepare the Server

1. Install IIS with the following features:
   - Web Server
   - Common HTTP Features
   - Application Development Features
   - Management Tools

2. Install Node.js LTS version:
   - Download from https://nodejs.org/
   - Install with default options

3. Install required IIS components:
   - Run the included `install-iisnode.ps1` script as Administrator
   - This script will install URL Rewrite Module and iisnode v0.2.26

### Step 2: Configure the Application

1. Extract the deployment package to a temporary location (e.g., `D:\PWVMS-Deployment`)

2. Configure the environment:
   - Navigate to the `backend` directory
   - Rename `.env.template` to `.env`
   - Edit `.env` and update the following settings:
     ```
     # Database Configuration
     DB_USER=your_database_user
     DB_PASSWORD=your_database_password
     DB_SERVER=your_database_server
     DB_NAME=your_database_name
     
     # Security
     JWT_SECRET=your-secure-production-secret-key
     
     # Application URL
     FRONTEND_URL=https://your-production-domain.com
     ```

### Step 3: Deploy to IIS

1. Run PowerShell as Administrator

2. Navigate to the extracted deployment package:
   ```powershell
   cd D:\PWVMS-Deployment
   ```

3. Run the deployment script:
   ```powershell
   .\deploy-to-iis.ps1
   ```

4. The script will:
   - Create the website in IIS
   - Configure the application pool
   - Copy all files to the IIS directory
   - Set appropriate permissions
   - Configure URL rewriting rules

### Step 4: Verify the Deployment

1. Open a web browser on the server

2. Navigate to:
   - http://localhost/ (or your configured hostname)

3. Test the API health check:
   - http://localhost/api/health-check

4. Verify you can log in to the application

### Step 5: Configure HTTPS (Recommended for Production)

1. Obtain an SSL certificate for your domain

2. In IIS Manager:
   - Select your PWVMS site
   - Click "Bindings" in the Actions panel
   - Add HTTPS binding with your SSL certificate
   - Optionally remove the HTTP binding or configure HTTP to HTTPS redirection

## Database Setup

The application requires a Microsoft SQL Server database. If you need to set up a new database:

1. Create a new database named `ParkwizOps` (or your preferred name)

2. Run the SQL scripts located in the `docs/sql` directory in this order:
   - `schema.sql` (database schema)
   - `initial_data.sql` (initial required data)
   - `sample_data.sql` (optional sample data)

3. Update the database connection settings in the `.env` file

## Post-Deployment Tasks

After successful deployment, perform these tasks:

1. **Create Administrator Account**:
   - Navigate to the registration page
   - Create an initial administrator account

2. **Configure System Settings**:
   - Set up company information
   - Configure plazas and lanes
   - Set up user roles and permissions

3. **Test Critical Functionality**:
   - User authentication
   - Plaza management
   - Lane configuration
   - Reporting features

## Troubleshooting

### Common Issues and Solutions

1. **Application Not Loading**:
   - Check IIS application pool is running
   - Verify web.config is correctly configured
   - Check iisnode is properly installed

2. **Backend API Errors**:
   - Check iisnode logs at `C:\inetpub\wwwroot\PWVMS\iisnode\`
   - Verify database connection settings
   - Check Node.js version compatibility

3. **Database Connection Issues**:
   - Verify SQL Server is accessible from the web server
   - Check firewall settings
   - Verify connection string in `.env` file

4. **File Upload Problems**:
   - Check permissions on the `Uploads` directories
   - Verify IIS AppPool identity has write access

### Log Locations

- **IIS Logs**: `C:\inetpub\logs\LogFiles\W3SVC1\`
- **Node.js Logs**: `C:\inetpub\wwwroot\PWVMS\iisnode\`
- **Application Logs**: `C:\inetpub\wwwroot\PWVMS\backend\logs\`

## Maintenance

### Backup Procedures

1. **Database Backup**:
   - Schedule regular SQL Server backups
   - Example SQL backup command:
     ```sql
     BACKUP DATABASE [ParkwizOps] TO DISK='C:\Backups\ParkwizOps.bak'
     ```

2. **Application Backup**:
   - Backup the entire website directory:
     ```powershell
     Copy-Item -Path "C:\inetpub\wwwroot\PWVMS" -Destination "C:\Backups\PWVMS-$(Get-Date -Format 'yyyyMMdd')" -Recurse
     ```

### Updating the Application

For future updates:

1. Stop the IIS application pool
2. Backup the current deployment
3. Extract the new deployment package
4. Run the deployment script
5. Start the IIS application pool

## Security Considerations

1. **Environment Variables**:
   - Ensure `.env` file is secured and not accessible via web
   - Use strong, unique values for JWT_SECRET

2. **Database Security**:
   - Use a dedicated database user with minimal required permissions
   - Enable SQL Server encryption

3. **HTTPS**:
   - Always use HTTPS in production
   - Configure HSTS for additional security

4. **File Permissions**:
   - Restrict access to application directories
   - Use least-privilege principle for service accounts

## Support Information

For technical support or questions about this deployment:

- **Contact**: [Your contact information]
- **Hours**: [Your support hours]
- **Response Time**: [Expected response time]

---

This handoff document was prepared on: [Current Date]

Version: 1.0