# IIS Deployment Script for PWVMS
# Run this script as administrator

# Configuration
$siteName = "PWVMS"
$appPoolName = "PWVMS_AppPool"
$sitePath = "C:\inetpub\wwwroot\PWVMS"
$port = 80
$sourceDir = $PSScriptRoot  # Current directory where the script is located

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "Please run this script as Administrator!"
    exit
}

# Check if IIS is installed
if ((Get-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole).State -ne "Enabled") {
    Write-Host "Installing IIS..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-ManagementConsole, IIS-HttpErrors, IIS-HttpRedirect, IIS-StaticContent, IIS-DefaultDocument, IIS-ApplicationDevelopment, IIS-NetFxExtensibility45, IIS-ASPNET45, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-WebSockets, IIS-ApplicationInit, IIS-HttpCompressionStatic, IIS-HttpCompressionDynamic -All
}

# Check if URL Rewrite module is installed
$rewriteModule = Get-WebGlobalModule | Where-Object { $_.Name -eq "RewriteModule" }
if ($null -eq $rewriteModule) {
    Write-Host "URL Rewrite Module is not installed. Please install it from: https://www.iis.net/downloads/microsoft/url-rewrite" -ForegroundColor Red
    Write-Host "After installing, restart this script." -ForegroundColor Red
    exit
}

# Check if iisnode is installed
$iisnodePath = "C:\Program Files\iisnode\iisnode.dll"
if (-not (Test-Path $iisnodePath)) {
    Write-Host "iisnode is not installed. Please install it from: https://github.com/Azure/iisnode/releases" -ForegroundColor Red
    Write-Host "After installing, restart this script." -ForegroundColor Red
    exit
}

# Create site directory if it doesn't exist
if (-not (Test-Path $sitePath)) {
    Write-Host "Creating site directory at $sitePath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $sitePath -Force
}

# Create deployment directories
$deploymentDirs = @(
    "$sitePath\backend",
    "$sitePath\backend\src",
    "$sitePath\backend\Uploads",
    "$sitePath\backend\Uploads\Companies",
    "$sitePath\backend\Uploads\Plazas",
    "$sitePath\frontend\build"
)

foreach ($dir in $deploymentDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Creating directory: $dir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Build the React frontend
Write-Host "Building React frontend..." -ForegroundColor Yellow
Set-Location -Path "$sourceDir\frontend"
npm install
npm run build
Set-Location -Path $sourceDir

# Copy backend files
Write-Host "Copying backend files..." -ForegroundColor Yellow
Copy-Item -Path "$sourceDir\backend\src\*" -Destination "$sitePath\backend\src" -Recurse -Force
Copy-Item -Path "$sourceDir\backend\package.json" -Destination "$sitePath\backend\package.json" -Force
Copy-Item -Path "$sourceDir\backend\package-lock.json" -Destination "$sitePath\backend\package-lock.json" -Force -ErrorAction SilentlyContinue

# Copy .env file or create from template if it doesn't exist
if (Test-Path "$sourceDir\backend\.env.production") {
    Write-Host "Copying production environment file..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\.env.production" -Destination "$sitePath\backend\.env" -Force
} elseif (Test-Path "$sourceDir\backend\.env") {
    Write-Host "Copying development environment file..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\.env" -Destination "$sitePath\backend\.env" -Force
} else {
    Write-Host "No environment file found. Please create one manually." -ForegroundColor Red
}

# Copy frontend build files
Write-Host "Copying frontend build files..." -ForegroundColor Yellow
Copy-Item -Path "$sourceDir\frontend\build\*" -Destination "$sitePath\frontend\build" -Recurse -Force

# Copy web.config
Write-Host "Copying web.config..." -ForegroundColor Yellow
Copy-Item -Path "$sourceDir\web.config" -Destination "$sitePath\web.config" -Force

# Install backend dependencies
Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
Set-Location -Path "$sitePath\backend"
npm install --production
Set-Location -Path $sourceDir

# Create application pool if it doesn't exist
if (-not (Get-IISAppPool -Name $appPoolName -ErrorAction SilentlyContinue)) {
    Write-Host "Creating application pool $appPoolName" -ForegroundColor Yellow
    New-WebAppPool -Name $appPoolName
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
}

# Create website if it doesn't exist
if (-not (Get-Website -Name $siteName -ErrorAction SilentlyContinue)) {
    Write-Host "Creating website $siteName" -ForegroundColor Yellow
    New-Website -Name $siteName -PhysicalPath $sitePath -ApplicationPool $appPoolName -Port $port -Force
} else {
    Write-Host "Updating website $siteName" -ForegroundColor Yellow
    Set-ItemProperty -Path "IIS:\Sites\$siteName" -Name "physicalPath" -Value $sitePath
    Set-ItemProperty -Path "IIS:\Sites\$siteName" -Name "applicationPool" -Value $appPoolName
}

# Set permissions
Write-Host "Setting folder permissions..." -ForegroundColor Yellow
$acl = Get-Acl $sitePath
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS AppPool\$appPoolName", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
Set-Acl $sitePath $acl

# Create uploads directories if they don't exist and set permissions
$uploadDirs = @(
    "$sitePath\backend\Uploads",
    "$sitePath\backend\Uploads\Companies",
    "$sitePath\backend\Uploads\Plazas"
)

foreach ($dir in $uploadDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Creating upload directory: $dir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
    
    # Set permissions for upload directories
    $acl = Get-Acl $dir
    $acl.SetAccessRule($accessRule)
    Set-Acl $dir $acl
}

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Your application should be accessible at http://localhost:$port" -ForegroundColor Green
Write-Host "Note: You may need to update the .env file in $sitePath\backend with your production database credentials." -ForegroundColor Yellow