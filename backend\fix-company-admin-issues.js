const db = require('./src/config/database');

async function fixCompanyAdminIssues() {
  try {
    console.log('=== FIXING COMPANY ADMIN ISSUES ===');
    
    // Step 1: Activate the company assignment
    console.log('\n1. ACTIVATING COMPANY ASSIGNMENT...');
    
    const activateResult = await db.query(`
      UPDATE UserCompany 
      SET IsActive = 1, ModifiedBy = 1, ModifiedOn = GETDATE()
      WHERE UserId = 4 AND CompanyId = 8
    `);
    
    console.log(`✓ Activated company assignment for CompanyAdmin (User ID: 4, Company ID: 8)`);
    console.log(`  Rows affected: ${activateResult.rowsAffected[0]}`);
    
    // Step 2: Verify the activation
    console.log('\n2. VERIFYING COMPANY ASSIGNMENT...');
    
    const verifyAssignment = await db.query(`
      SELECT 
        uc.UserId,
        uc.CompanyId,
        c.CompanyName,
        uc.IsActive,
        uc.ModifiedOn
      FROM UserCompany uc
      JOIN Company c ON uc.CompanyId = c.Id
      WHERE uc.UserId = 4
    `);
    
    verifyAssignment.recordset.forEach(assignment => {
      console.log(`  ✓ User 4 -> Company: ${assignment.CompanyName} (ID: ${assignment.CompanyId})`);
      console.log(`    Active: ${assignment.IsActive}`);
      console.log(`    Modified: ${assignment.ModifiedOn}`);
    });
    
    // Step 3: Check what data CompanyAdmin should now see
    console.log('\n3. CHECKING ACCESSIBLE DATA AFTER FIX...');
    
    // Companies
    const accessibleCompanies = await db.query(`
      SELECT c.Id, c.CompanyName, c.CountryName, c.StateName
      FROM Company c
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND c.IsActive = 1
    `);
    
    console.log(`Accessible companies: ${accessibleCompanies.recordset.length}`);
    accessibleCompanies.recordset.forEach(company => {
      console.log(`  ✓ ${company.CompanyName} (${company.CountryName}, ${company.StateName})`);
    });
    
    // Plazas
    const accessiblePlazas = await db.query(`
      SELECT p.Id, p.PlazaName, c.CompanyName
      FROM Plaza p
      JOIN Company c ON p.CompanyId = c.Id
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND p.IsActive = 1
    `);
    
    console.log(`Accessible plazas: ${accessiblePlazas.recordset.length}`);
    accessiblePlazas.recordset.forEach(plaza => {
      console.log(`  ✓ ${plaza.PlazaName} (${plaza.CompanyName})`);
    });
    
    // Lanes
    const accessibleLanes = await db.query(`
      SELECT l.Id, l.LaneName, p.PlazaName, c.CompanyName
      FROM Lane l
      JOIN Plaza p ON l.PlazaId = p.Id
      JOIN Company c ON p.CompanyId = c.Id
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND l.IsActive = 1
    `);
    
    console.log(`Accessible lanes: ${accessibleLanes.recordset.length}`);
    accessibleLanes.recordset.forEach(lane => {
      console.log(`  ✓ ${lane.LaneName} (${lane.PlazaName} - ${lane.CompanyName})`);
    });
    
    // Users (should include users assigned to same company or plazas within that company)
    const accessibleUsers = await db.query(`
      SELECT DISTINCT u.Id, u.Username, u.Email, r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.Id IN (
        -- Users assigned to same companies
        SELECT DISTINCT uc2.UserId
        FROM UserCompany uc1
        JOIN UserCompany uc2 ON uc1.CompanyId = uc2.CompanyId
        WHERE uc1.UserId = 4 AND uc1.IsActive = 1 AND uc2.IsActive = 1
        
        UNION
        
        -- Users assigned to plazas within the company
        SELECT DISTINCT up.UserId
        FROM UserCompany uc
        JOIN Plaza p ON uc.CompanyId = p.CompanyId
        JOIN UserPlaza up ON p.Id = up.PlazaId
        WHERE uc.UserId = 4 AND uc.IsActive = 1 AND up.IsActive = 1
      )
      AND u.IsActive = 1
      AND r.Name != 'SuperAdmin'  -- Exclude SuperAdmin users
      ORDER BY r.Name, u.Username
    `);
    
    console.log(`Accessible users (excluding SuperAdmin): ${accessibleUsers.recordset.length}`);
    accessibleUsers.recordset.forEach(user => {
      console.log(`  ✓ ${user.Username} (${user.Email}) - ${user.RoleName}`);
    });
    
    // Step 4: Create additional company assignments for testing
    console.log('\n4. ADDING MORE COMPANY ASSIGNMENTS FOR TESTING...');
    
    // Get a few more companies to assign
    const moreCompanies = await db.query(`
      SELECT TOP 3 Id, CompanyName 
      FROM Company 
      WHERE Id != 8 AND IsActive = 1
      ORDER BY CompanyName
    `);
    
    for (const company of moreCompanies.recordset) {
      // Check if assignment already exists
      const existingAssignment = await db.query(`
        SELECT Id FROM UserCompany 
        WHERE UserId = 4 AND CompanyId = ${company.Id}
      `);
      
      if (existingAssignment.recordset.length === 0) {
        await db.query(`
          INSERT INTO UserCompany (UserId, CompanyId, IsActive, CreatedBy, CreatedOn)
          VALUES (4, ${company.Id}, 1, 1, GETDATE())
        `);
        console.log(`  ✓ Added assignment: CompanyAdmin -> ${company.CompanyName}`);
      } else {
        // Activate existing assignment
        await db.query(`
          UPDATE UserCompany 
          SET IsActive = 1, ModifiedBy = 1, ModifiedOn = GETDATE()
          WHERE UserId = 4 AND CompanyId = ${company.Id}
        `);
        console.log(`  ✓ Activated assignment: CompanyAdmin -> ${company.CompanyName}`);
      }
    }
    
    // Step 5: Final verification
    console.log('\n5. FINAL VERIFICATION...');
    
    const finalCompanies = await db.query(`
      SELECT c.Id, c.CompanyName, uc.IsActive
      FROM UserCompany uc
      JOIN Company c ON uc.CompanyId = c.Id
      WHERE uc.UserId = 4
      ORDER BY c.CompanyName
    `);
    
    console.log('Final company assignments for CompanyAdmin:');
    finalCompanies.recordset.forEach(assignment => {
      console.log(`  ${assignment.IsActive ? '✓' : '✗'} ${assignment.CompanyName} (ID: ${assignment.Id})`);
    });
    
    const finalPlazas = await db.query(`
      SELECT COUNT(*) as PlazaCount
      FROM Plaza p
      JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND p.IsActive = 1
    `);
    
    const finalLanes = await db.query(`
      SELECT COUNT(*) as LaneCount
      FROM Lane l
      JOIN Plaza p ON l.PlazaId = p.Id
      JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND l.IsActive = 1
    `);
    
    console.log(`\nData now accessible to CompanyAdmin:`);
    console.log(`  Companies: ${finalCompanies.recordset.filter(c => c.IsActive).length}`);
    console.log(`  Plazas: ${finalPlazas.recordset[0].PlazaCount}`);
    console.log(`  Lanes: ${finalLanes.recordset[0].LaneCount}`);
    
    console.log('\n=== COMPANY ADMIN DATA ACCESS FIXED ===');
    console.log('\n✅ COMPLETED:');
    console.log('1. ✓ Activated company assignments for CompanyAdmin');
    console.log('2. ✓ Added additional company assignments for testing');
    console.log('3. ✓ Verified data accessibility');
    
    console.log('\n🔧 STILL NEEDED (Backend/Frontend Fixes):');
    console.log('1. Restrict user creation to prevent SuperAdmin role creation');
    console.log('2. Filter user list to exclude SuperAdmin users');
    console.log('3. Prevent CompanyAdmin from editing themselves');
    console.log('4. Update frontend user filtering logic');
    
    console.log('\n📋 TEST NOW:');
    console.log('Login as CompanyAdmin (company admin / PASSWORD) and verify:');
    console.log('- Companies are now visible');
    console.log('- Plazas are now visible');
    console.log('- Lanes are now visible');
    console.log('- Users section shows relevant users (excluding SuperAdmin)');
    
    process.exit(0);
  } catch (error) {
    console.error('Fix failed:', error);
    process.exit(1);
  }
}

fixCompanyAdminIssues();
