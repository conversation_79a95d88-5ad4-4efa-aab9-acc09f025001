﻿<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <!-- Set default document -->
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
      </files>
    </defaultDocument>
    
    <!-- Redirect all traffic to HTTPS -->
    <rewrite>
      <rules>
        <!-- Commented out HTTP to HTTPS redirect
        <rule name="HTTP to HTTPS" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="^OFF$" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
        </rule>
        -->

        <!-- API requests -->
        <rule name="API" stopProcessing="true">
          <match url="^api/(.*)" />
          <action type="Rewrite" url="backend/src/server.js" />
        </rule>

        <!-- Static files -->
        <rule name="StaticFiles" stopProcessing="true">
          <match url=".*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$" />
          <action type="Rewrite" url="frontend/build/{REQUEST_URI}" />
        </rule>

        <!-- Default route to React app -->
        <rule name="ReactRouter" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/api" negate="true" />
          </conditions>
          <action type="Rewrite" url="frontend/build/index.html" />
        </rule>
      </rules>
    </rewrite>

    <!-- Configure iisnode -->
    <iisnode
      node_env="production"
      nodeProcessCommandLine="C:\Program Files\nodejs\node.exe"
      watchedFiles="*.js;iisnode.yml;node_modules\*;routes\*.js;views\*.jade"
      loggingEnabled="true"
      logDirectory="iisnode"
      debuggingEnabled="false"
      maxNamedPipeConnectionRetry="100"
      namedPipeConnectionRetryDelay="250"
      maxNamedPipeConnectionPoolSize="512"
      maxNamedPipePooledConnectionAge="30000"
      asyncCompletionThreadCount="0"
      initialRequestBufferSize="4096"
      maxRequestBufferSize="65536"
      uncFileChangesPollingInterval="5000"
      gracefulShutdownTimeout="60000"
      maxConcurrentRequestsPerProcess="1024"
      maxNamedPipeConnectionLifetime="5000"
    />

    <!-- Don't show directory listings -->
    <directoryBrowse enabled="false" />

    <!-- Configure static content handling -->
    <staticContent>
      <remove fileExtension=".json" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <remove fileExtension=".woff" />
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <remove fileExtension=".woff2" />
      <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
    </staticContent>

    <!-- Configure handlers -->
    <handlers>
      <add name="iisnode" path="backend/src/server.js" verb="*" modules="iisnode" />
    </handlers>
  </system.webServer>
</configuration>
