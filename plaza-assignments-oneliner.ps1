# One-liner PowerShell command to get plaza assignments
# Usage: .\plaza-assignments-oneliner.ps1

Write-Host "=== PLAZA ASSIGNMENTS QUICK REPORT ===" -ForegroundColor Cyan

# Run the Node.js script from the backend directory
Set-Location "d:/PWVMS/backend"
node "../get-plaza-assignments-simple.js"

Write-Host "`nTo get detailed information, you can also run:" -ForegroundColor Yellow
Write-Host "cd d:/PWVMS/backend && node ../get-plaza-assignments-simple.js" -ForegroundColor White