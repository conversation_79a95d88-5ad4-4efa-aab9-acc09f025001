// Comprehensive fix for all lane fields based on database schema analysis
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'backend/src/controllers/LaneController.js');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

console.log('🔧 Fixing lane field defaults based on database schema...');

// Fields that don't allow NULL and need specific defaults
const fieldDefaults = {
  // Numeric fields that don't allow NULL
  'LOTFee': '0',
  'iGraceMinute': '0',
  
  // String fields that don't allow NULL (use database defaults or empty string)
  'LaneDetails': "''",
  'VehicleType': "''",
  'UpdatedBy': 'updatedBy', // This should use the actual user ID
  'PGTID': "'12345678'", // Database default
  'pgActivationKey': "'1234567812345678'", // Database default
  'Passcard_Reader_Type': "'False'", // Database default
  'LPRCamIP': "'*******'", // Database default
  'LPRCamID': "'NA'", // Database default
  'LPRCamPass': "'NA'", // Database default
  'PGSLevel': "'L1'", // Database default
  'PrinterMake': "'NA'", // Database default
  'BarcodeType': "'NA'", // Database default
  'PrinterPort': "'00'", // Database default
  'sPaytmWallet': "'False'", // Database default
  'sPaytmMID': "'NA'", // Database default
  'sPaytmKey': "'NA'", // Database default
  'sSMSKey': "'NA'", // Database default
  'LaneNumber2': "'0'", // Database default
  'VehicleType2': "'NA'", // Database default
  'RecyclerType': "'NA'", // Database default
  
  // Boolean-like char fields that don't allow NULL
  'flgPasscard': "'N'", // Database default
  'PayTmPG': "'False'", // Database default
  'FlgLPRCamera': "'False'", // Database default
  'flgPaperSensor': "'N'", // Database default
  'fRecyclerStatus': "'False'", // Database default
  'flgCCUpdateEx': "'N'", // Database default
  'flgSubLane': "'N'" // Database default
};

// Replace each field in the createLane function (first occurrence)
Object.entries(fieldDefaults).forEach(([field, defaultValue]) => {
  const oldPattern = `        ${field}: ${field} || null,`;
  const newPattern = `        ${field}: ${field} || ${defaultValue},`;
  
  const firstIndex = content.indexOf(oldPattern);
  if (firstIndex !== -1) {
    content = content.substring(0, firstIndex) + newPattern + content.substring(firstIndex + oldPattern.length);
    console.log(`✅ Fixed ${field} field -> default: ${defaultValue}`);
  } else {
    console.log(`⚠️  Could not find ${field} field pattern`);
  }
});

// Write the file back
fs.writeFileSync(filePath, content, 'utf8');
console.log('\n✅ All lane field defaults updated successfully');
console.log('🔄 Please restart your backend server to apply the changes.');
console.log('\n📋 Summary of changes:');
console.log('- Numeric fields: Use 0 as default');
console.log('- String fields: Use database defaults or appropriate values');
console.log('- Boolean-like fields: Use database defaults (N/False)');
console.log('- UpdatedBy: Uses actual user ID from request');