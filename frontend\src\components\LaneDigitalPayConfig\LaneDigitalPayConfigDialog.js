import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';
import { plazaApi } from '../../api/plazaApi';
import { useQuery } from '@tanstack/react-query';
import './LaneDigitalPayConfigDialog.css';

export default function LaneDigitalPayConfigDialog({ isOpen, onClose, onSubmit, initialData, title, plazas: allPlazas, companies, lanes }) {
  const { user } = useAuth();
  
  // State to track the selected company's plazas
  const [companyPlazas, setCompanyPlazas] = useState([]);
  const [formData, setFormData] = useState({
    PlazaID: '',
    CompanyID: '',
    LaneID: '',
    LaneNumber: '',
    EnableCardPayment: 'N',
    CardReaderPGProvider: '',
    CardReaderPort: '',
    CardReaderEnvType: '',
    CardReaderUserName: '',
    CardReaderPassword: '',
    CardReaderAppKey: '',
    CardPayAPIURL: '',
    CardReaderDeviceID: '',
    CardReaderMID: '',
    EnableUPIPhonePe: 'N',
    UPIPGProvider: '',
    PhonePeMID: '',
    PhonePeKeyID: '',
    PhonePeIndexID: '',
    PhonePeAPI: '',
    EnableSendSMS: 'N',
    SMSSenderName: '',
    SMSAPI: '',
    UpdatedBy: 'admin', // Default value for UpdatedBy
    ActiveStatus: 'Y',
    CardPaymentDeviceModel: '',
    AllowBlacklistedVehicle: 'N'
  });

  const [errors, setErrors] = useState({});
  const [availableLanes, setAvailableLanes] = useState([]);

  // Helper function to check if a toggle is enabled
  const isToggleEnabled = (value) => {
    // Handle various true values
    if (value === 'Y' || value === 1 || value === true || value === '1' || value === 'true' || value === 'yes') {
      return true;
    }

    // Handle various false values
    if (value === 'N' || value === 0 || value === false || value === '0' || value === 'false' || value === 'no' || value === null || value === undefined) {
      return false;
    }

    // For any other case, try to parse the value
    try {
      // If it's a string that can be parsed as a number
      const numValue = Number(value);
      if (!isNaN(numValue)) {
        return numValue > 0;
      }
    } catch (e) {
      console.error(`Error parsing value ${value}:`, e);
    }

    return false; // Default to false for any other value
  };

  // Initialize form data when initialData changes or dialog opens
  useEffect(() => {
    if (initialData) {
      console.log('Digital Pay Dialog - Initial data:', initialData);
      console.log('Digital Pay Dialog - Initial ActiveStatus:', initialData.ActiveStatus, typeof initialData.ActiveStatus);

      // Use the isToggleEnabled helper function to normalize values
      const normalizeValue = (value) => {
        const result = isToggleEnabled(value) ? 'Y' : 'N';
        console.log(`Digital Pay Dialog - Normalizing value ${value} to ${result}`);
        return result;
      };

      // Create new form data with normalized boolean values
      const newFormData = {
        PlazaID: initialData.PlazaID || '',
        CompanyID: initialData.CompanyID || '',
        LaneID: initialData.LaneID || '',
        LaneNumber: initialData.LaneNumber || '',
        EnableCardPayment: normalizeValue(initialData.EnableCardPayment),
        CardReaderPGProvider: initialData.CardReaderPGProvider || '',
        CardReaderPort: initialData.CardReaderPort || '',
        CardReaderEnvType: initialData.CardReaderEnvType || '',
        CardReaderUserName: initialData.CardReaderUserName || '',
        CardReaderPassword: initialData.CardReaderPassword || '',
        CardReaderAppKey: initialData.CardReaderAppKey || '',
        CardPayAPIURL: initialData.CardPayAPIURL || '',
        CardReaderDeviceID: initialData.CardReaderDeviceID || '',
        CardReaderMID: initialData.CardReaderMID || '',
        EnableUPIPhonePe: normalizeValue(initialData.EnableUPIPhonePe),
        UPIPGProvider: initialData.UPIPGProvider || '',
        PhonePeMID: initialData.PhonePeMID || '',
        PhonePeKeyID: initialData.PhonePeKeyID || '',
        PhonePeIndexID: initialData.PhonePeIndexID || '',
        PhonePeAPI: initialData.PhonePeAPI || '',
        EnableSendSMS: normalizeValue(initialData.EnableSendSMS),
        SMSSenderName: initialData.SMSSenderName || '',
        SMSAPI: initialData.SMSAPI || '',
        UpdatedBy: initialData.UpdatedBy || 'admin',
        ActiveStatus: normalizeValue(initialData.ActiveStatus),
        CardPaymentDeviceModel: initialData.CardPaymentDeviceModel || '',
        AllowBlacklistedVehicle: normalizeValue(initialData.AllowBlacklistedVehicle)
      };

      console.log('Digital Pay Dialog - Normalized form data:', {
        EnableCardPayment: newFormData.EnableCardPayment,
        EnableUPIPhonePe: newFormData.EnableUPIPhonePe,
        EnableSendSMS: newFormData.EnableSendSMS,
        ActiveStatus: newFormData.ActiveStatus,
        AllowBlacklistedVehicle: newFormData.AllowBlacklistedVehicle
      });

      setFormData(newFormData);
    } else {
      resetForm();
    }
  }, [initialData, isOpen]);

  // Fetch plazas for the selected company
  const { data: fetchedCompanyPlazas, isLoading: plazasLoading, error: plazasError } = useQuery({
    queryKey: ['plazasByCompany', formData.CompanyID],
    queryFn: async () => {
      try {
        // Log the API call
        console.log(`Calling API: /companies/${formData.CompanyID}/plazas`);
        
        // Make the API call
        const data = await plazaApi.getPlazasByCompany(formData.CompanyID);
        
        // Log the response
        console.log(`API response for company ${formData.CompanyID} plazas:`, data);
        
        return data;
      } catch (error) {
        console.error(`API call failed for company ${formData.CompanyID} plazas:`, error);
        throw error;
      }
    },
    enabled: !!formData.CompanyID, // Only run the query if a company is selected
    onSuccess: (data) => {
      console.log('Setting company plazas state with:', data);
      setCompanyPlazas(data || []);
    },
    onError: (error) => {
      console.error('Error fetching plazas for company:', error);
    }
  });

  // Filter lanes based on selected plaza
  useEffect(() => {
    if (formData.PlazaID && lanes) {
      const filteredLanes = lanes.filter(lane =>
        lane.PlazaID === formData.PlazaID ||
        lane.PlazaID === Number(formData.PlazaID)
      );
      setAvailableLanes(filteredLanes);
    } else {
      setAvailableLanes([]);
    }
  }, [formData.PlazaID, lanes]);

  // Update LaneNumber when LaneID changes
  useEffect(() => {
    if (formData.LaneID && availableLanes.length > 0) {
      const selectedLane = availableLanes.find(lane =>
        lane.LaneID === formData.LaneID ||
        lane.LaneID === Number(formData.LaneID)
      );
      if (selectedLane) {
        console.log('Selected lane:', selectedLane);
        setFormData(prev => ({
          ...prev,
          LaneNumber: selectedLane.LaneNumber
        }));
        console.log('Updated LaneNumber to:', selectedLane.LaneNumber);
      }
    }
  }, [formData.LaneID, availableLanes]);



  const resetForm = () => {
    const defaultFormData = {
      PlazaID: '',
      CompanyID: '',
      LaneID: '',
      LaneNumber: '',
      EnableCardPayment: 'N',
      CardReaderPGProvider: '',
      CardReaderPort: '',
      CardReaderEnvType: '',
      CardReaderUserName: '',
      CardReaderPassword: '',
      CardReaderAppKey: '',
      CardPayAPIURL: '',
      CardReaderDeviceID: '',
      CardReaderMID: '',
      EnableUPIPhonePe: 'N',
      UPIPGProvider: '',
      PhonePeMID: '',
      PhonePeKeyID: '',
      PhonePeIndexID: '',
      PhonePeAPI: '',
      EnableSendSMS: 'N',
      SMSSenderName: '',
      SMSAPI: '',
      UpdatedBy: 'admin',
      ActiveStatus: 'Y',
      CardPaymentDeviceModel: '',
      AllowBlacklistedVehicle: 'N'
    };

    setFormData(defaultFormData);
    setErrors({});
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // For checkboxes, convert boolean to string 'Y' or 'N' to match database format
    const newValue = type === 'checkbox' ? (checked ? 'Y' : 'N') : value;

    // Create a new form data object with the updated value
    let updatedFormData = {
      ...formData,
      [name]: newValue
    };

    // If company changes, reset plaza and lane
    if (name === 'CompanyID') {
      console.log('Company changed, resetting PlazaID and LaneID');
      updatedFormData = {
        ...updatedFormData,
        PlazaID: '',
        LaneID: '',
        LaneNumber: ''
      };
    }

    // If plaza changes, reset lane
    if (name === 'PlazaID') {
      console.log('Plaza changed, resetting LaneID');
      updatedFormData = {
        ...updatedFormData,
        LaneID: '',
        LaneNumber: ''
      };
    }

    setFormData(updatedFormData);

    // Clear error for this field if any
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.PlazaID) newErrors.PlazaID = 'Plaza is required';
    if (!formData.CompanyID) newErrors.CompanyID = 'Company is required';
    if (!formData.LaneID) newErrors.LaneID = 'Lane is required';
    if (!formData.LaneNumber) newErrors.LaneNumber = 'Lane number is required';
    if (!formData.UpdatedBy) newErrors.UpdatedBy = 'Updated By is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Convert Y/N to 1/0 for the API
      const processedData = {
        ...formData,
        LaneNumber: formData.LaneNumber ? String(formData.LaneNumber) : '',
        // Convert 'Y'/'N' to '1'/'0' for API
        EnableCardPayment: formData.EnableCardPayment === 'Y' ? '1' : '0',
        EnableUPIPhonePe: formData.EnableUPIPhonePe === 'Y' ? '1' : '0',
        EnableSendSMS: formData.EnableSendSMS === 'Y' ? '1' : '0',
        ActiveStatus: formData.ActiveStatus === 'Y' ? '1' : '0',
        AllowBlacklistedVehicle: formData.AllowBlacklistedVehicle === 'Y' ? '1' : '0',
        // Add user tracking
        UpdatedBy: user?.id || formData.UpdatedBy || 'admin'
      };

      onSubmit(processedData);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center px-6 py-4 border-b">
          <h2 className="text-lg font-medium">{title}</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information Section */}
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Company Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company*
                  </label>
                  <select
                    name="CompanyID"
                    value={formData.CompanyID}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md ${
                      errors.CompanyID ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select Company</option>
                    {(() => {
                      // Handle different possible data structures
                      let companyItems = companies;

                      // If companies is not an array but has a companies property that is an array
                      if (companies && !Array.isArray(companies) && companies.companies && Array.isArray(companies.companies)) {
                        companyItems = companies.companies;
                      }

                      // If companies is not an array but has a data property that is an array
                      if (companies && !Array.isArray(companies) && companies.data && Array.isArray(companies.data)) {
                        companyItems = companies.data;
                      }

                      if (!Array.isArray(companyItems)) {
                        return <option value="">No companies available</option>;
                      }

                      return companyItems.map((company) => {
                        // Check if company has the required properties
                        const id = company.Id || company.id;
                        const name = company.CompanyName || company.companyName || company.name;

                        if (id && name) {
                          return (
                            <option key={id} value={id}>
                              {name}
                            </option>
                          );
                        }
                        return null;
                      });
                    })()}
                  </select>
                  {errors.CompanyID && (
                    <p className="mt-1 text-sm text-red-500">{errors.CompanyID}</p>
                  )}
                </div>

                {/* Plaza Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Plaza*
                  </label>
                  <select
                    name="PlazaID"
                    value={formData.PlazaID}
                    onChange={handleChange}
                    disabled={!formData.CompanyID}
                    className={`w-full px-3 py-2 border rounded-md ${
                      errors.PlazaID ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    {!formData.CompanyID ? (
                      <option value="">Select a company first</option>
                    ) : plazasLoading ? (
                      <option value="">Loading plazas...</option>
                    ) : (
                      <>
                        <option value="">Select Plaza</option>
                        {(() => {
                          if (plazasError) {
                            console.error('Error loading plazas:', plazasError);
                            return <option value="">Error loading plazas</option>;
                          }
                          
                          if (fetchedCompanyPlazas) {
                            // Handle different response structures
                            let plazaItems = [];
                            
                            if (Array.isArray(fetchedCompanyPlazas)) {
                              plazaItems = fetchedCompanyPlazas;
                            } else if (fetchedCompanyPlazas.plazas && Array.isArray(fetchedCompanyPlazas.plazas)) {
                              plazaItems = fetchedCompanyPlazas.plazas;
                            } else if (fetchedCompanyPlazas.data && Array.isArray(fetchedCompanyPlazas.data)) {
                              plazaItems = fetchedCompanyPlazas.data;
                            }
                            
                            if (plazaItems.length > 0) {
                              console.log('Rendering plaza options:', plazaItems);
                              
                              return plazaItems.map((plaza) => {
                                // Check if plaza has the required properties
                                const id = plaza.Id || plaza.id || plaza.PlazaID || plaza.plazaID;
                                const name = plaza.PlazaName || plaza.plazaName || plaza.name;
                                
                                console.log('Plaza option:', id, name);

                                if (id && name) {
                                  return (
                                    <option key={id} value={id}>
                                      {name}
                                    </option>
                                  );
                                }
                                return null;
                              });
                            }
                          }
                          
                          return <option value="">No plazas available for this company</option>;
                        })()}
                      </>
                    )}
                  </select>
                  {errors.PlazaID && (
                    <p className="mt-1 text-sm text-red-500">{errors.PlazaID}</p>
                  )}
                </div>

                {/* Lane Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Lane*
                  </label>
                  <select
                    name="LaneID"
                    value={formData.LaneID}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md ${
                      errors.LaneID ? 'border-red-500' : 'border-gray-300'
                    }`}
                    disabled={!formData.PlazaID}
                  >
                    <option value="">Select Lane</option>
                    {availableLanes.map((lane) => (
                      <option key={lane.LaneID} value={lane.LaneID}>
                        {lane.LaneNumber} - {lane.LaneType}
                      </option>
                    ))}
                  </select>
                  {errors.LaneID && (
                    <p className="mt-1 text-sm text-red-500">{errors.LaneID}</p>
                  )}
                </div>

                {/* Updated By */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Updated By*
                  </label>
                  <input
                    type="text"
                    name="UpdatedBy"
                    value={formData.UpdatedBy}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md ${
                      errors.UpdatedBy ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.UpdatedBy && (
                    <p className="mt-1 text-sm text-red-500">{errors.UpdatedBy}</p>
                  )}
                </div>

                {/* Active Status */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="activeStatus"
                    name="ActiveStatus"
                    checked={isToggleEnabled(formData.ActiveStatus)}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="activeStatus" className="ml-2 text-sm font-medium text-gray-700">
                    Active Status
                  </label>
                </div>

                {/* Allow Blacklisted Vehicle */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="allowBlacklistedVehicle"
                    name="AllowBlacklistedVehicle"
                    checked={isToggleEnabled(formData.AllowBlacklistedVehicle)}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="allowBlacklistedVehicle" className="ml-2 text-sm font-medium text-gray-700">
                    Allow Blacklisted Vehicle
                  </label>
                </div>
              </div>
            </div>

            {/* Card Payment Section */}
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Card Payment Configuration
                {!isToggleEnabled(formData.EnableCardPayment) &&
                  <span className="ml-2 text-sm text-gray-500 font-normal">(Enable Card Payment to edit these fields)</span>
                }
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Enable Card Payment - Always outside the disabled section */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableCardPayment"
                    name="EnableCardPayment"
                    checked={isToggleEnabled(formData.EnableCardPayment)}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="enableCardPayment" className="ml-2 text-sm font-medium text-gray-700">
                    Enable Card Payment
                  </label>
                </div>

                {/* Empty div for grid alignment */}
                <div></div>

                {/* Rest of the fields in a potentially disabled section */}
                <div className={`col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 toggle-section ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'disabled'}`}>


                {/* Card Payment Device Model */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Payment Device Model
                  </label>
                  <input
                    type="text"
                    name="CardPaymentDeviceModel"
                    value={formData.CardPaymentDeviceModel}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>

                {/* Card Reader PG Provider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Reader PG Provider
                  </label>
                  <input
                    type="text"
                    name="CardReaderPGProvider"
                    value={formData.CardReaderPGProvider}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                  {!isToggleEnabled(formData.EnableCardPayment) && (
                    <p className="mt-1 text-xs text-gray-500">Enable Card Payment to edit this field</p>
                  )}
                </div>

                {/* Card Reader Port */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Reader Port
                  </label>
                  <input
                    type="text"
                    name="CardReaderPort"
                    value={formData.CardReaderPort}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>

                {/* Card Reader Environment Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Reader Environment Type
                  </label>
                  <input
                    type="text"
                    name="CardReaderEnvType"
                    value={formData.CardReaderEnvType}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>

                {/* Card Reader Username */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Reader Username
                  </label>
                  <input
                    type="text"
                    name="CardReaderUserName"
                    value={formData.CardReaderUserName}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>

                {/* Card Reader Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Reader Password
                  </label>
                  <input
                    type="password"
                    name="CardReaderPassword"
                    value={formData.CardReaderPassword}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>

                {/* Card Reader App Key */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Reader App Key
                  </label>
                  <input
                    type="text"
                    name="CardReaderAppKey"
                    value={formData.CardReaderAppKey}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>

                {/* Card Pay API URL */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Pay API URL
                  </label>
                  <input
                    type="text"
                    name="CardPayAPIURL"
                    value={formData.CardPayAPIURL}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>

                {/* Card Reader Device ID */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Reader Device ID
                  </label>
                  <input
                    type="text"
                    name="CardReaderDeviceID"
                    value={formData.CardReaderDeviceID}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>

                {/* Card Reader MID */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Reader MID
                  </label>
                  <input
                    type="text"
                    name="CardReaderMID"
                    value={formData.CardReaderMID}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableCardPayment) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableCardPayment)}
                  />
                </div>
                </div> {/* Close the toggle-section div */}
              </div>
            </div>

            {/* UPI/PhonePe Section */}
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                UPI/PhonePe Configuration
                {!isToggleEnabled(formData.EnableUPIPhonePe) &&
                  <span className="ml-2 text-sm text-gray-500 font-normal">(Enable UPI/PhonePe to edit these fields)</span>
                }
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Enable UPI/PhonePe - Always outside the disabled section */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableUPIPhonePe"
                    name="EnableUPIPhonePe"
                    checked={isToggleEnabled(formData.EnableUPIPhonePe)}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="enableUPIPhonePe" className="ml-2 text-sm font-medium text-gray-700">
                    Enable UPI/PhonePe
                  </label>
                </div>

                {/* Empty div for grid alignment */}
                <div></div>

                {/* Rest of the fields in a potentially disabled section */}
                <div className={`col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 toggle-section ${isToggleEnabled(formData.EnableUPIPhonePe) ? '' : 'disabled'}`}>

                {/* UPI PG Provider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    UPI PG Provider
                  </label>
                  <input
                    type="text"
                    name="UPIPGProvider"
                    value={formData.UPIPGProvider}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableUPIPhonePe) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableUPIPhonePe)}
                  />
                </div>

                {/* PhonePe MID */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    PhonePe MID
                  </label>
                  <input
                    type="text"
                    name="PhonePeMID"
                    value={formData.PhonePeMID}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableUPIPhonePe) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableUPIPhonePe)}
                  />
                </div>

                {/* PhonePe Key ID */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    PhonePe Key ID
                  </label>
                  <input
                    type="text"
                    name="PhonePeKeyID"
                    value={formData.PhonePeKeyID}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableUPIPhonePe) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableUPIPhonePe)}
                  />
                </div>

                {/* PhonePe Index ID */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    PhonePe Index ID
                  </label>
                  <input
                    type="text"
                    name="PhonePeIndexID"
                    value={formData.PhonePeIndexID}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableUPIPhonePe) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableUPIPhonePe)}
                  />
                </div>

                {/* PhonePe API */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    PhonePe API
                  </label>
                  <input
                    type="text"
                    name="PhonePeAPI"
                    value={formData.PhonePeAPI}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableUPIPhonePe) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableUPIPhonePe)}
                  />
                </div>
                </div> {/* Close the toggle-section div */}
              </div>
            </div>

            {/* SMS Section */}
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                SMS Configuration
                {!isToggleEnabled(formData.EnableSendSMS) &&
                  <span className="ml-2 text-sm text-gray-500 font-normal">(Enable Send SMS to edit these fields)</span>
                }
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Enable Send SMS - Always outside the disabled section */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableSendSMS"
                    name="EnableSendSMS"
                    checked={isToggleEnabled(formData.EnableSendSMS)}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="enableSendSMS" className="ml-2 text-sm font-medium text-gray-700">
                    Enable Send SMS
                  </label>
                </div>

                {/* Empty div for grid alignment */}
                <div></div>

                {/* Rest of the fields in a potentially disabled section */}
                <div className={`col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 toggle-section ${isToggleEnabled(formData.EnableSendSMS) ? '' : 'disabled'}`}>

                {/* SMS Sender Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SMS Sender Name
                  </label>
                  <input
                    type="text"
                    name="SMSSenderName"
                    value={formData.SMSSenderName}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableSendSMS) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableSendSMS)}
                  />
                </div>

                {/* SMS API */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SMS API
                  </label>
                  <input
                    type="text"
                    name="SMSAPI"
                    value={formData.SMSAPI}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md ${isToggleEnabled(formData.EnableSendSMS) ? '' : 'bg-gray-100'}`}
                    disabled={!isToggleEnabled(formData.EnableSendSMS)}
                  />
                </div>
                </div> {/* Close the toggle-section div */}
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <PermissionButton
              type="submit"
              className="px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              requiredModule="DigitalPayment"
              requiredPermissions={initialData ? ["Edit"] : ["Create"]}
              companyId={initialData?.CompanyID}
              plazaId={initialData?.PlazaID}
            >
              {initialData ? 'Update' : 'Create'}
            </PermissionButton>
          </div>
        </form>
      </div>
    </div>
  );
}