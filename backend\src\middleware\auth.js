const jwt = require('jsonwebtoken');
const db = require('../config/database');
const { responseHandler } = require('../Utils/ResponseHandler');

/**
 * Authentication middleware
 * Verifies JWT token and adds user information to request
 * Optionally checks for specific permissions or access rights
 *
 * @param {Array} requiredPermissions - Array of permissions required to access the route
 * @param {Object} options - Additional options for access control
 * @returns {Function} Express middleware function
 */
const auth = (requiredPermissions = [], options = {}) => {
  return async (req, res, next) => {
    try {
      // Get token from header
      const token = req.header('Authorization')?.replace('Bearer ', '');

      if (!token) {
        return responseHandler.unauthorized(res, 'No authentication token provided');
      }

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
      const userId = decoded.userId;

      // Get user with role information
      const userQuery = `
        SELECT u.*, r.Name as RoleName
        FROM Users u
        JOIN Roles r ON u.RoleId = r.Id
        WHERE u.Id = @userId AND u.IsActive = 1
      `;

      const userResult = await db.query(userQuery, { userId });

      if (userResult.recordset.length === 0) {
        return responseHandler.unauthorized(res, 'Invalid authentication token');
      }

      const user = userResult.recordset[0];

      // Add user to request
      req.user = {
        id: user.Id,
        role: user.RoleName,
        roleId: user.RoleId,
        firstName: user.FirstName,
        lastName: user.LastName
      };

      // Check if SuperAdmin (bypass all checks)
      if (user.RoleName === 'SuperAdmin') {
        return next();
      }

      // Check permissions if required
      if (requiredPermissions.length > 0) {
        const permissionQuery = `
          SELECT DISTINCT p.Name
          FROM RolePermissions rp
          JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
          JOIN Permissions p ON smp.PermissionId = p.Id
          WHERE rp.RoleId = @roleId AND rp.IsActive = 1
        `;

        const permissionResult = await db.query(permissionQuery, { roleId: user.RoleId });
        const userPermissions = permissionResult.recordset.map(p => p.Name);

        const hasRequiredPermission = requiredPermissions.some(permission =>
          userPermissions.includes(permission)
        );

        if (!hasRequiredPermission) {
          return responseHandler.forbidden(res, 'You do not have permission to perform this action');
        }
      }

      // Check company access if needed
      const companyId = req.params.companyId || req.body.companyId || req.query.companyId || req.body.CompanyId;
      if (companyId && user.RoleName === 'CompanyAdmin') {
        const companyAccessQuery = `
          SELECT 1 FROM UserCompany
          WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
        `;

        const companyAccessResult = await db.query(companyAccessQuery, {
          userId: user.Id,
          companyId
        });

        if (companyAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this company');
        }
      }

      // Check plaza access if needed
      const plazaId = req.params.plazaId || req.body.plazaId || req.query.plazaId || req.body.PlazaID;
      if (plazaId && user.RoleName === 'PlazaManager') {
        const plazaAccessQuery = `
          SELECT 1 FROM UserPlaza
          WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
        `;

        const plazaAccessResult = await db.query(plazaAccessQuery, {
          userId: user.Id,
          plazaId
        });

        if (plazaAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this plaza');
        }
      }

      // If CompanyAdmin is accessing a plaza, check if the plaza belongs to their company
      if (plazaId && user.RoleName === 'CompanyAdmin') {
        const plazaCompanyQuery = `
          SELECT 1
          FROM Plaza p
          JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
          WHERE p.Id = @plazaId AND uc.UserId = @userId AND uc.IsActive = 1
        `;

        const plazaCompanyResult = await db.query(plazaCompanyQuery, {
          userId: user.Id,
          plazaId
        });

        if (plazaCompanyResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this plaza');
        }
      }

      // If PlazaManager is accessing a lane, check if the lane belongs to their plaza
      const laneId = req.params.laneId || req.body.laneId || req.query.laneId || req.body.LaneID;
      if (laneId && user.RoleName === 'PlazaManager') {
        const lanePlazaQuery = `
          SELECT 1
          FROM tblLaneDetails l
          JOIN UserPlaza up ON l.PlazaID = up.PlazaId
          WHERE l.LaneID = @laneId AND up.UserId = @userId AND up.IsActive = 1
        `;

        const lanePlazaResult = await db.query(lanePlazaQuery, {
          userId: user.Id,
          laneId
        });

        if (lanePlazaResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this lane');
        }
      }

      // If CompanyAdmin is accessing a lane, check if the lane belongs to their company
      if (laneId && user.RoleName === 'CompanyAdmin') {
        const laneCompanyQuery = `
          SELECT 1
          FROM tblLaneDetails l
          JOIN UserCompany uc ON l.CompanyID = uc.CompanyId
          WHERE l.LaneID = @laneId AND uc.UserId = @userId AND uc.IsActive = 1
        `;

        const laneCompanyResult = await db.query(laneCompanyQuery, {
          userId: user.Id,
          laneId
        });

        if (laneCompanyResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this lane');
        }
      }

      next();
    } catch (error) {
      console.error('Auth middleware error:', error);
      
      // Check if the error is due to token expiration
      if (error.name === 'TokenExpiredError') {
        return responseHandler.unauthorized(res, 'Your session has expired. Please log in again.');
      }
      
      // Check if the error is due to invalid token
      if (error.name === 'JsonWebTokenError') {
        return responseHandler.unauthorized(res, 'Invalid authentication token');
      }
      
      return responseHandler.unauthorized(res, 'Authentication failed');
    }
  };
};

module.exports = auth;