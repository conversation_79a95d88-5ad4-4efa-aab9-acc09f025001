// Test script to verify dashboard filtering according to user roles
// This script tests if the dashboard properly filters data based on user assignments

const db = require('./backend/src/config/database');

async function testDashboardFiltering() {
  try {
    console.log('=== TESTING DASHBOARD FILTERING LOGIC ===\n');
    
    // Test data: accr user details
    const accrUserId = 5;
    const accrRole = 'PlazaManager';
    
    console.log('Testing for user: accr (PlazaManager)');
    console.log('User ID:', accrUserId);
    console.log('Role:', accrRole);
    
    // 1. Test PlazaManager filtering logic
    console.log('\n1. TESTING PLAZA MANAGER FILTERING');
    console.log('=====================================');
    
    // Get plazas assigned to accr user
    const userPlazasQuery = `
      SELECT 
        p.Id as PlazaId,
        p.Plaza<PERSON>,
        p.Plaza<PERSON>,
        c.<PERSON>,
        up.IsActive as AssignmentActive
      FROM UserPlaza up
      INNER JOIN Plaza p ON up.PlazaId = p.Id
      INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE up.UserId = @userId AND up.IsActive = 1
      ORDER BY p.PlazaName
    `;
    
    const userPlazas = await db.query(userPlazasQuery, { userId: accrUserId });
    
    console.log(`\nPlazas assigned to accr user: ${userPlazas.recordset.length}`);
    userPlazas.recordset.forEach(plaza => {
      console.log(`  • ${plaza.PlazaName} (${plaza.PlazaCode}) - ${plaza.CompanyName}`);
    });
    
    // 2. Test the actual dashboard filtering query (as used in DashboardController)
    console.log('\n2. TESTING DASHBOARD QUERY FILTERING');
    console.log('====================================');
    
    // Simulate the dashboard query with PlazaManager filtering
    const dashboardFilterQuery = `
      SELECT 
        t.PlazaCode,
        t.PlazaName,
        COUNT(*) as TransactionCount,
        SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as TotalRevenue
      FROM tblParkwiz_Parking_Data t
      WHERE t.PlazaCode IN (
        SELECT p.PlazaCode 
        FROM Plaza p 
        JOIN UserPlaza up ON p.Id = up.PlazaId 
        WHERE up.UserId = @userId AND up.IsActive = 1
      )
      AND t.ExitDateTime >= DATEADD(DAY, -30, GETDATE())
      GROUP BY t.PlazaCode, t.PlazaName
      ORDER BY t.PlazaName
    `;
    
    const dashboardData = await db.query(dashboardFilterQuery, { userId: accrUserId });
    
    console.log(`\nDashboard data accessible to accr user: ${dashboardData.recordset.length} plazas`);
    dashboardData.recordset.forEach(data => {
      console.log(`  • ${data.PlazaName} (${data.PlazaCode}): ${data.TransactionCount} transactions, ₹${data.TotalRevenue} revenue`);
    });
    
    // 3. Test what data should NOT be accessible
    console.log('\n3. TESTING DATA ISOLATION');
    console.log('=========================');
    
    // Get all plazas in the system
    const allPlazasQuery = `
      SELECT 
        p.Id,
        p.PlazaName,
        p.PlazaCode,
        c.CompanyName
      FROM Plaza p
      INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE p.IsActive = 1
      ORDER BY p.PlazaName
    `;
    
    const allPlazas = await db.query(allPlazasQuery);
    
    console.log(`\nTotal plazas in system: ${allPlazas.recordset.length}`);
    console.log(`Plazas accessible to accr: ${userPlazas.recordset.length}`);
    console.log(`Plazas NOT accessible to accr: ${allPlazas.recordset.length - userPlazas.recordset.length}`);
    
    // Find plazas that accr should NOT have access to
    const accessiblePlazaCodes = userPlazas.recordset.map(p => p.PlazaCode);
    const restrictedPlazas = allPlazas.recordset.filter(p => !accessiblePlazaCodes.includes(p.PlazaCode));
    
    if (restrictedPlazas.length > 0) {
      console.log('\nPlazas that accr should NOT access:');
      restrictedPlazas.forEach(plaza => {
        console.log(`  • ${plaza.PlazaName} (${plaza.PlazaCode}) - ${plaza.CompanyName}`);
      });
    }
    
    // 4. Test CompanyAdmin filtering logic
    console.log('\n4. TESTING COMPANY ADMIN FILTERING LOGIC');
    console.log('========================================');
    
    // Get companies assigned to accr user
    const userCompaniesQuery = `
      SELECT 
        c.Id as CompanyId,
        c.CompanyName,
        uc.IsActive as AssignmentActive
      FROM UserCompany uc
      INNER JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
      WHERE uc.UserId = @userId AND uc.IsActive = 1
    `;
    
    const userCompanies = await db.query(userCompaniesQuery, { userId: accrUserId });
    
    console.log(`\nCompanies assigned to accr user: ${userCompanies.recordset.length}`);
    userCompanies.recordset.forEach(company => {
      console.log(`  • ${company.CompanyName} (ID: ${company.CompanyId})`);
    });
    
    // 5. Test SuperAdmin access (should see everything)
    console.log('\n5. TESTING SUPER ADMIN ACCESS');
    console.log('=============================');
    
    const superAdminQuery = `
      SELECT 
        t.PlazaCode,
        t.PlazaName,
        COUNT(*) as TransactionCount,
        SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as TotalRevenue
      FROM tblParkwiz_Parking_Data t
      WHERE t.ExitDateTime >= DATEADD(DAY, -30, GETDATE())
      GROUP BY t.PlazaCode, t.PlazaName
      ORDER BY t.PlazaName
    `;
    
    const superAdminData = await db.query(superAdminQuery);
    
    console.log(`\nData accessible to SuperAdmin: ${superAdminData.recordset.length} plazas`);
    console.log('(SuperAdmin should see ALL data across ALL companies and plazas)');
    
    // 6. Verify filtering effectiveness
    console.log('\n6. FILTERING EFFECTIVENESS ANALYSIS');
    console.log('==================================');
    
    const plazaManagerAccessible = dashboardData.recordset.length;
    const totalDataPoints = superAdminData.recordset.length;
    const restrictionPercentage = ((totalDataPoints - plazaManagerAccessible) / totalDataPoints * 100).toFixed(1);
    
    console.log(`PlazaManager (accr) can access: ${plazaManagerAccessible} out of ${totalDataPoints} plazas`);
    console.log(`Data restriction: ${restrictionPercentage}% of data is properly restricted`);
    
    if (plazaManagerAccessible === userPlazas.recordset.length) {
      console.log('✅ FILTERING IS WORKING CORRECTLY: PlazaManager only sees assigned plazas');
    } else {
      console.log('❌ FILTERING ISSUE: PlazaManager sees different data than expected');
    }
    
    // 7. Test specific dashboard endpoints filtering
    console.log('\n7. TESTING DASHBOARD ENDPOINT FILTERING');
    console.log('=======================================');
    
    // Test the exact filtering logic from DashboardController
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7); // Last 7 days
    const endDate = new Date();
    
    const dashboardSummaryQuery = `
      SELECT
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
        COUNT(*) as TransactionCount,
        COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)
    `;
    
    const summaryResult = await db.query(dashboardSummaryQuery, { 
      userId: accrUserId, 
      startDate, 
      endDate 
    });
    
    const summary = summaryResult.recordset[0];
    console.log('\nDashboard Summary for accr (PlazaManager):');
    console.log(`  Total Revenue: ₹${summary.TotalRevenue}`);
    console.log(`  Transaction Count: ${summary.TransactionCount}`);
    console.log(`  Unique Vehicles: ${summary.VehicleCount}`);
    console.log(`  Average Duration: ${summary.AvgDuration} minutes`);
    
    // Compare with unrestricted data
    const unrestrictedSummaryQuery = `
      SELECT
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
        COUNT(*) as TransactionCount,
        COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
    `;
    
    const unrestrictedResult = await db.query(unrestrictedSummaryQuery, { startDate, endDate });
    const unrestrictedSummary = unrestrictedResult.recordset[0];
    
    console.log('\nUnrestricted Dashboard Summary (SuperAdmin view):');
    console.log(`  Total Revenue: ₹${unrestrictedSummary.TotalRevenue}`);
    console.log(`  Transaction Count: ${unrestrictedSummary.TransactionCount}`);
    console.log(`  Unique Vehicles: ${unrestrictedSummary.VehicleCount}`);
    console.log(`  Average Duration: ${unrestrictedSummary.AvgDuration} minutes`);
    
    // Calculate data restriction effectiveness
    const revenueRestriction = ((unrestrictedSummary.TotalRevenue - summary.TotalRevenue) / unrestrictedSummary.TotalRevenue * 100).toFixed(1);
    const transactionRestriction = ((unrestrictedSummary.TransactionCount - summary.TransactionCount) / unrestrictedSummary.TransactionCount * 100).toFixed(1);
    
    console.log('\nData Restriction Analysis:');
    console.log(`  Revenue restricted: ${revenueRestriction}%`);
    console.log(`  Transactions restricted: ${transactionRestriction}%`);
    
    // 8. Final assessment
    console.log('\n8. FINAL ASSESSMENT');
    console.log('==================');
    
    console.log('\n✅ EXPECTED BEHAVIOR:');
    console.log('  • SuperAdmin: Can see ALL data across ALL companies and plazas');
    console.log('  • CompanyAdmin: Can only see data from their assigned companies');
    console.log('  • PlazaManager: Can only see data from their assigned plazas');
    
    console.log('\n📊 CURRENT ACCR USER STATUS:');
    console.log(`  • Role: ${accrRole}`);
    console.log(`  • Assigned to ${userPlazas.recordset.length} plazas across ${userCompanies.recordset.length} companies`);
    console.log(`  • Can access ${plazaManagerAccessible} plazas worth of data`);
    console.log(`  • ${restrictionPercentage}% of system data is properly restricted`);
    
    if (plazaManagerAccessible === userPlazas.recordset.length && restrictionPercentage > 0) {
      console.log('\n🎉 CONCLUSION: Dashboard filtering is working correctly!');
      console.log('   The PlazaManager can only see data from assigned plazas.');
    } else {
      console.log('\n⚠️  CONCLUSION: There may be issues with dashboard filtering.');
      console.log('   Please review the filtering logic in the dashboard controller.');
    }
    
    console.log('\n=== TEST COMPLETE ===');
    process.exit(0);
    
  } catch (error) {
    console.error('Error during testing:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

testDashboardFiltering();