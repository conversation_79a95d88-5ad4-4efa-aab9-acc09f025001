const db = require('./src/config/database');

async function investigateCompanyAdmin() {
  try {
    console.log('=== INVESTIGATING COMPANY ADMIN USER ===');
    
    // Step 1: Find the company admin user
    console.log('\n1. FINDING COMPANY ADMIN USER...');
    
    const companyAdminUser = await db.query(`
      SELECT 
        u.Id,
        u.Username,
        u.Email,
        u.FirstName,
        u.LastName,
        r.Name as RoleName,
        u.IsActive,
        u.CreatedBy,
        u.CreatedOn
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.Username = 'company admin'
    `);
    
    if (companyAdminUser.recordset.length === 0) {
      console.log('❌ Company admin user not found!');
      return;
    }
    
    const user = companyAdminUser.recordset[0];
    console.log('✓ Found company admin user:');
    console.log(`  ID: ${user.Id}`);
    console.log(`  Username: ${user.Username}`);
    console.log(`  Email: ${user.Email}`);
    console.log(`  Role: ${user.RoleName}`);
    console.log(`  Active: ${user.IsActive}`);
    
    // Step 2: Check company assignments
    console.log('\n2. CHECKING COMPANY ASSIGNMENTS...');
    
    const companyAssignments = await db.query(`
      SELECT 
        uc.Id as UserCompanyId,
        uc.CompanyId,
        c.CompanyName,
        c.CountryName,
        c.StateName,
        uc.IsActive as AssignmentActive,
        uc.CreatedBy,
        uc.CreatedOn
      FROM UserCompany uc
      JOIN Company c ON uc.CompanyId = c.Id
      WHERE uc.UserId = ${user.Id}
      ORDER BY uc.CreatedOn DESC
    `);
    
    console.log(`Company assignments: ${companyAssignments.recordset.length}`);
    if (companyAssignments.recordset.length === 0) {
      console.log('❌ NO COMPANY ASSIGNMENTS FOUND! This is why CompanyAdmin sees nothing.');
      
      // Show available companies
      const availableCompanies = await db.query('SELECT TOP 5 Id, CompanyName FROM Company WHERE IsActive = 1');
      console.log('\nAvailable companies to assign:');
      availableCompanies.recordset.forEach(company => {
        console.log(`  ID: ${company.Id} - ${company.CompanyName}`);
      });
    } else {
      companyAssignments.recordset.forEach(assignment => {
        console.log(`  ✓ Company: ${assignment.CompanyName} (ID: ${assignment.CompanyId})`);
        console.log(`    Location: ${assignment.CountryName}, ${assignment.StateName}`);
        console.log(`    Active: ${assignment.AssignmentActive}`);
      });
    }
    
    // Step 3: Check plaza assignments (if any)
    console.log('\n3. CHECKING PLAZA ASSIGNMENTS...');
    
    const plazaAssignments = await db.query(`
      SELECT 
        up.Id as UserPlazaId,
        up.PlazaId,
        p.PlazaName,
        c.CompanyName,
        up.IsActive as AssignmentActive
      FROM UserPlaza up
      JOIN Plaza p ON up.PlazaId = p.Id
      JOIN Company c ON p.CompanyId = c.Id
      WHERE up.UserId = ${user.Id}
    `);
    
    console.log(`Plaza assignments: ${plazaAssignments.recordset.length}`);
    plazaAssignments.recordset.forEach(assignment => {
      console.log(`  ✓ Plaza: ${assignment.PlazaName} (Company: ${assignment.CompanyName})`);
    });
    
    // Step 4: Check what data they should see
    console.log('\n4. CHECKING ACCESSIBLE DATA...');
    
    if (companyAssignments.recordset.length > 0) {
      const companyIds = companyAssignments.recordset
        .filter(a => a.AssignmentActive)
        .map(a => a.CompanyId);
      
      if (companyIds.length > 0) {
        // Check plazas in their companies
        const accessiblePlazas = await db.query(`
          SELECT p.Id, p.PlazaName, c.CompanyName
          FROM Plaza p
          JOIN Company c ON p.CompanyId = c.Id
          WHERE p.CompanyId IN (${companyIds.join(',')}) AND p.IsActive = 1
        `);
        
        console.log(`Accessible plazas: ${accessiblePlazas.recordset.length}`);
        accessiblePlazas.recordset.forEach(plaza => {
          console.log(`  ✓ ${plaza.PlazaName} (${plaza.CompanyName})`);
        });
        
        // Check lanes in their plazas
        const accessibleLanes = await db.query(`
          SELECT l.Id, l.LaneName, p.PlazaName, c.CompanyName
          FROM Lane l
          JOIN Plaza p ON l.PlazaId = p.Id
          JOIN Company c ON p.CompanyId = c.Id
          WHERE p.CompanyId IN (${companyIds.join(',')}) AND l.IsActive = 1
        `);
        
        console.log(`Accessible lanes: ${accessibleLanes.recordset.length}`);
        accessibleLanes.recordset.forEach(lane => {
          console.log(`  ✓ ${lane.LaneName} (${lane.PlazaName} - ${lane.CompanyName})`);
        });
        
        // Check users in their companies
        const accessibleUsers = await db.query(`
          SELECT DISTINCT u.Id, u.Username, u.Email, r.Name as RoleName
          FROM Users u
          JOIN Roles r ON u.RoleId = r.Id
          LEFT JOIN UserCompany uc ON u.Id = uc.UserId
          LEFT JOIN UserPlaza up ON u.Id = up.UserId
          LEFT JOIN Plaza p ON up.PlazaId = p.Id
          WHERE (uc.CompanyId IN (${companyIds.join(',')}) OR p.CompanyId IN (${companyIds.join(',')}))
          AND u.IsActive = 1
          ORDER BY r.Name, u.Username
        `);
        
        console.log(`Accessible users: ${accessibleUsers.recordset.length}`);
        accessibleUsers.recordset.forEach(accessUser => {
          console.log(`  ✓ ${accessUser.Username} (${accessUser.Email}) - ${accessUser.RoleName}`);
        });
      }
    }
    
    // Step 5: Check user creation permissions
    console.log('\n5. CHECKING USER CREATION PERMISSIONS...');
    
    const userPermissions = await db.query(`
      SELECT 
        m.Name as ModuleName,
        sm.Name as SubModuleName,
        p.Name as PermissionName
      FROM RolePermissions rp
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE rp.RoleId = (SELECT RoleId FROM Users WHERE Id = ${user.Id})
      AND m.Name = 'User Management'
      AND rp.IsActive = 1
      ORDER BY sm.Name, p.Name
    `);
    
    console.log('User Management permissions:');
    userPermissions.recordset.forEach(perm => {
      console.log(`  ✓ ${perm.SubModuleName} -> ${perm.PermissionName}`);
    });
    
    // Step 6: Check recent user creations by this admin
    console.log('\n6. CHECKING RECENT USER CREATIONS...');
    
    const recentCreations = await db.query(`
      SELECT 
        u.Id,
        u.Username,
        u.Email,
        r.Name as RoleName,
        u.CreatedOn
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.CreatedBy = ${user.Id}
      ORDER BY u.CreatedOn DESC
    `);
    
    console.log(`Users created by this admin: ${recentCreations.recordset.length}`);
    recentCreations.recordset.forEach(created => {
      console.log(`  ✓ ${created.Username} (${created.RoleName}) - ${created.CreatedOn}`);
    });
    
    console.log('\n=== ISSUES IDENTIFIED ===');
    
    if (companyAssignments.recordset.length === 0) {
      console.log('❌ CRITICAL: CompanyAdmin has NO company assignments');
      console.log('   This is why they see no data (companies, plazas, lanes, users)');
    }
    
    console.log('❌ PERMISSION ISSUE: CompanyAdmin can create SuperAdmin users');
    console.log('   This should be restricted to only create users within their companies');
    
    console.log('\n=== FIXES NEEDED ===');
    console.log('1. Assign companies to CompanyAdmin user');
    console.log('2. Fix user creation permissions to prevent SuperAdmin creation');
    console.log('3. Update user filtering to show only company-related users');
    console.log('4. Prevent CompanyAdmin from editing themselves');
    
    process.exit(0);
  } catch (error) {
    console.error('Investigation failed:', error);
    process.exit(1);
  }
}

investigateCompanyAdmin();
