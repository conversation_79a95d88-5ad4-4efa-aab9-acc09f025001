const db = require('./src/config/database');

async function simpleModuleCheck() {
  try {
    console.log('=== SIMPLE MODULE CHECK ===');
    
    // Check SubModules table structure
    const subModulesStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'SubModules'
      ORDER BY ORDINAL_POSITION
    `);
    console.log('SubModules table structure:', subModulesStructure.recordset);
    
    // Get all submodules without DisplayOrder
    const subModules = await db.query(`
      SELECT sm.*, m.Name as ModuleName
      FROM SubModules sm
      JOIN Modules m ON sm.ModuleId = m.Id
      ORDER BY m.Id, sm.Id
    `);
    
    console.log('\nAll SubModules:');
    subModules.recordset.forEach(sm => {
      console.log(`${sm.ModuleName} -> ${sm.Name} (${sm.Path})`);
    });
    
    // Check what permissions CompanyAdmin has
    console.log('\n=== COMPANYADMIN PERMISSIONS ===');
    const companyAdminPerms = await db.query(`
      SELECT 
        m.Name as ModuleName,
        sm.Name as SubModuleName,
        sm.Path,
        p.Name as PermissionName
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE r.Name = 'CompanyAdmin' AND rp.IsActive = 1
      ORDER BY m.Id, sm.Id, p.Id
    `);
    
    console.log(`CompanyAdmin has ${companyAdminPerms.recordset.length} permissions:`);
    
    // Group by module
    const moduleGroups = {};
    companyAdminPerms.recordset.forEach(perm => {
      if (!moduleGroups[perm.ModuleName]) {
        moduleGroups[perm.ModuleName] = {};
      }
      if (!moduleGroups[perm.ModuleName][perm.SubModuleName]) {
        moduleGroups[perm.ModuleName][perm.SubModuleName] = [];
      }
      moduleGroups[perm.ModuleName][perm.SubModuleName].push(perm.PermissionName);
    });
    
    Object.keys(moduleGroups).forEach(moduleName => {
      console.log(`\n${moduleName}:`);
      Object.keys(moduleGroups[moduleName]).forEach(subModuleName => {
        console.log(`  ${subModuleName}: ${moduleGroups[moduleName][subModuleName].join(', ')}`);
      });
    });
    
    // Check what permissions PlazaManager has
    console.log('\n=== PLAZAMANAGER PERMISSIONS ===');
    const plazaManagerPerms = await db.query(`
      SELECT 
        m.Name as ModuleName,
        sm.Name as SubModuleName,
        sm.Path,
        p.Name as PermissionName
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE r.Name = 'PlazaManager' AND rp.IsActive = 1
      ORDER BY m.Id, sm.Id, p.Id
    `);
    
    console.log(`PlazaManager has ${plazaManagerPerms.recordset.length} permissions:`);
    
    // Group by module
    const plazaModuleGroups = {};
    plazaManagerPerms.recordset.forEach(perm => {
      if (!plazaModuleGroups[perm.ModuleName]) {
        plazaModuleGroups[perm.ModuleName] = {};
      }
      if (!plazaModuleGroups[perm.ModuleName][perm.SubModuleName]) {
        plazaModuleGroups[perm.ModuleName][perm.SubModuleName] = [];
      }
      plazaModuleGroups[perm.ModuleName][perm.SubModuleName].push(perm.PermissionName);
    });
    
    Object.keys(plazaModuleGroups).forEach(moduleName => {
      console.log(`\n${moduleName}:`);
      Object.keys(plazaModuleGroups[moduleName]).forEach(subModuleName => {
        console.log(`  ${subModuleName}: ${plazaModuleGroups[moduleName][subModuleName].join(', ')}`);
      });
    });
    
    // Check if Plaza Management module exists and has permissions
    console.log('\n=== PLAZA MANAGEMENT CHECK ===');
    const plazaManagementCheck = await db.query(`
      SELECT 
        sm.Name as SubModuleName,
        sm.Path,
        COUNT(smp.Id) as PermissionCount
      FROM SubModules sm
      JOIN Modules m ON sm.ModuleId = m.Id
      LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId AND smp.IsActive = 1
      WHERE m.Name = 'Plaza Management'
      GROUP BY sm.Id, sm.Name, sm.Path
    `);
    
    console.log('Plaza Management submodules:', plazaManagementCheck.recordset);
    
    process.exit(0);
  } catch (error) {
    console.error('Simple module check failed:', error);
    process.exit(1);
  }
}

simpleModuleCheck();
