// Node.js script to show plaza assignments by company
// This script shows how many plazas are assigned to which companies

const db = require('./backend/src/config/database');

async function getCompanyPlazaAssignments() {
  try {
    console.log('=== COMPANY-PLAZA ASSIGNMENTS REPORT ===');
    console.log('Connecting to database: ParkwizOps\n');
    
    // Query to get all companies and their plazas
    console.log('Fetching company-plaza data...');
    const companyPlazaQuery = `
      SELECT 
        c.Id as CompanyId,
        c.CompanyName,
        c.<PERSON> as CompanyContactPerson,
        c.ContactNumber as CompanyContactNumber,
        c.ContactEmail as CompanyEmail,
        p.Id as PlazaId,
        p.Plaza<PERSON>ame,
        p.<PERSON>,
        p.<PERSON> as PlazaContactPerson,
        p.ContactNumber as PlazaContactNumber,
        p.IsActive as PlazaActive,
        p.CreatedOn as PlazaCreatedOn
      FROM tblCompanyMaster c
      LEFT JOIN Plaza p ON c.Id = p.CompanyId
      WHERE c.IsActive = 1
      ORDER BY c.Company<PERSON>, p.<PERSON>
    `;
    
    const results = await db.query(companyPlazaQuery);
    
    if (results.recordset.length > 0) {
      console.log('\nCOMPANY-PLAZA ASSIGNMENTS:');
      
      // Group by company
      const companyGroups = {};
      results.recordset.forEach(row => {
        if (!companyGroups[row.CompanyName]) {
          companyGroups[row.CompanyName] = [];
        }
        companyGroups[row.CompanyName].push(row);
      });
      
      let totalCompanies = 0;
      let totalPlazas = 0;
      let companiesWithPlazas = 0;
      let companiesWithoutPlazas = 0;
      const companyPlazaCounts = [];
      
      Object.keys(companyGroups).forEach(companyName => {
        const companyData = companyGroups[companyName];
        const company = companyData[0];
        totalCompanies++;
        
        console.log(`\n--- COMPANY: ${company.CompanyName} ---`);
        console.log(`Company ID: ${company.CompanyId}`);
        console.log(`Contact: ${company.CompanyContactPerson}`);
        console.log(`Phone: ${company.CompanyContactNumber}`);
        console.log(`Email: ${company.CompanyEmail}`);
        
        // Check if company has plazas
        const allPlazas = companyData.filter(row => row.PlazaId !== null);
        const activePlazas = allPlazas.filter(row => row.PlazaActive === true);
        const inactivePlazas = allPlazas.filter(row => row.PlazaActive === false);
        
        if (allPlazas.length > 0) {
          companiesWithPlazas++;
          console.log(`Plazas: ${allPlazas.length} total (${activePlazas.length} active, ${inactivePlazas.length} inactive)`);
          
          allPlazas.forEach(plaza => {
            const status = plaza.PlazaActive ? "ACTIVE" : "INACTIVE";
            console.log(`  • ${plaza.PlazaName} (Code: ${plaza.PlazaCode}) - ${status}`);
            console.log(`    Contact: ${plaza.PlazaContactPerson} - ${plaza.PlazaContactNumber}`);
            console.log(`    Created: ${plaza.PlazaCreatedOn}`);
          });
          totalPlazas += allPlazas.length;
        } else {
          companiesWithoutPlazas++;
          console.log('Plazas: 0 (No plazas assigned)');
        }
        
        // Store for summary
        companyPlazaCounts.push({
          CompanyName: companyName,
          TotalPlazas: allPlazas.length,
          ActivePlazas: activePlazas.length
        });
      });
      
      // Summary statistics
      console.log('\n=== SUMMARY STATISTICS ===');
      console.log(`Total Companies: ${totalCompanies}`);
      console.log(`Companies with Plazas: ${companiesWithPlazas}`);
      console.log(`Companies without Plazas: ${companiesWithoutPlazas}`);
      console.log(`Total Plazas: ${totalPlazas}`);
      
      // Top companies by plaza count
      console.log('\n=== TOP COMPANIES BY PLAZA COUNT ===');
      companyPlazaCounts
        .sort((a, b) => b.TotalPlazas - a.TotalPlazas)
        .forEach(company => {
          if (company.TotalPlazas > 0) {
            console.log(`${company.CompanyName}: ${company.TotalPlazas} plazas (${company.ActivePlazas} active)`);
          }
        });
        
    } else {
      console.log('No company-plaza data found.');
    }
    
    // Additional query: Show user assignments by company
    console.log('\n=== USER ASSIGNMENTS BY COMPANY ===');
    
    const userCompanyQuery = `
      SELECT 
        c.CompanyName,
        u.Username,
        u.FirstName + ' ' + u.LastName as FullName,
        u.Email,
        r.Name as RoleName,
        p.PlazaName,
        p.PlazaCode
      FROM tblCompanyMaster c
      INNER JOIN Plaza p ON c.Id = p.CompanyId
      INNER JOIN UserPlaza up ON p.Id = up.PlazaId
      INNER JOIN Users u ON up.UserId = u.Id
      LEFT JOIN Roles r ON u.RoleId = r.Id
      WHERE c.IsActive = 1 AND p.IsActive = 1 AND u.IsActive = 1
      ORDER BY c.CompanyName, u.Username
    `;
    
    const userResults = await db.query(userCompanyQuery);
    
    if (userResults.recordset.length > 0) {
      const userCompanyGroups = {};
      userResults.recordset.forEach(row => {
        if (!userCompanyGroups[row.CompanyName]) {
          userCompanyGroups[row.CompanyName] = [];
        }
        userCompanyGroups[row.CompanyName].push(row);
      });
      
      Object.keys(userCompanyGroups).forEach(companyName => {
        console.log(`\n${companyName}:`);
        userCompanyGroups[companyName].forEach(assignment => {
          console.log(`  • ${assignment.Username} (${assignment.FullName}) - ${assignment.RoleName}`);
          console.log(`    Plaza: ${assignment.PlazaName} (${assignment.PlazaCode})`);
          console.log(`    Email: ${assignment.Email}`);
        });
      });
    } else {
      console.log('No user assignments found for any company plazas.');
    }
    
    console.log('\n=== REPORT COMPLETE ===');
    process.exit(0);
    
  } catch (error) {
    console.error('Error executing query:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

getCompanyPlazaAssignments();