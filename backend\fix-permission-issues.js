const db = require('./src/config/database');

async function fixPermissionIssues() {
  try {
    console.log('=== FIXING PERMISSION ISSUES ===');
    
    // Step 1: Create Company view for tblCompanyMaster
    console.log('\n1. CREATING COMPANY VIEW...');
    
    try {
      await db.query('DROP VIEW IF EXISTS Company');
      await db.query(`
        CREATE VIEW Company AS
        SELECT 
          Id,
          CompanyName,
          AddressId,
          ContactPerson,
          ContactNumber,
          ContactEmail,
          CompanyLogo,
          CompanyCode,
          IsActive,
          CreatedBy,
          CreatedOn,
          ModifiedBy,
          ModifiedOn
        FROM tblCompanyMaster
      `);
      console.log('✓ Company view created successfully');
    } catch (error) {
      console.error('Error creating Company view:', error.message);
    }
    
    // Step 2: Add missing Plaza Management permissions for CompanyAdmin and PlazaManager
    console.log('\n2. CHECKING PLAZA MANAGEMENT PERMISSIONS...');
    
    // Check if Plaza Management permissions exist for CompanyAdmin and PlazaManager
    const plazaPermissionsCheck = await db.query(`
      SELECT 
        r.Name as RoleName,
        COUNT(*) as PlazaPermissions
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      WHERE m.Name = 'Plaza Management' 
      AND r.Name IN ('CompanyAdmin', 'PlazaManager')
      AND rp.IsActive = 1
      GROUP BY r.Id, r.Name
    `);
    
    console.log('Current Plaza Management permissions:', plazaPermissionsCheck.recordset);
    
    // Get Plaza Management submodules
    const plazaSubModules = await db.query(`
      SELECT sm.Id, sm.Name, sm.Path
      FROM SubModules sm
      JOIN Modules m ON sm.ModuleId = m.Id
      WHERE m.Name = 'Plaza Management'
    `);
    
    console.log('Plaza Management submodules:', plazaSubModules.recordset);
    
    // Step 3: Add missing permissions for CompanyAdmin and PlazaManager
    console.log('\n3. ADDING MISSING PERMISSIONS...');
    
    // Get role IDs
    const roles = await db.query('SELECT Id, Name FROM Roles WHERE Name IN (\'CompanyAdmin\', \'PlazaManager\')');
    const companyAdminId = roles.recordset.find(r => r.Name === 'CompanyAdmin')?.Id;
    const plazaManagerId = roles.recordset.find(r => r.Name === 'PlazaManager')?.Id;
    
    console.log('CompanyAdmin ID:', companyAdminId);
    console.log('PlazaManager ID:', plazaManagerId);
    
    // Add Plaza Management permissions for CompanyAdmin (View, Create, Edit, Delete)
    if (companyAdminId && plazaSubModules.recordset.length > 0) {
      for (const subModule of plazaSubModules.recordset) {
        // Get all permissions for this submodule
        const subModulePermissions = await db.query(`
          SELECT Id FROM SubModulePermissions 
          WHERE SubModuleId = ${subModule.Id} AND IsActive = 1
        `);
        
        for (const smp of subModulePermissions.recordset) {
          // Check if permission already exists
          const existingPermission = await db.query(`
            SELECT Id FROM RolePermissions 
            WHERE RoleId = ${companyAdminId} AND SubModulePermissionId = ${smp.Id}
          `);
          
          if (existingPermission.recordset.length === 0) {
            await db.query(`
              INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
              VALUES (${companyAdminId}, ${smp.Id}, 1, 1, GETDATE())
            `);
            console.log(`✓ Added permission for CompanyAdmin: ${subModule.Name}`);
          }
        }
      }
    }
    
    // Add Plaza Management permissions for PlazaManager (View, Create, Edit, Delete)
    if (plazaManagerId && plazaSubModules.recordset.length > 0) {
      for (const subModule of plazaSubModules.recordset) {
        // Get all permissions for this submodule
        const subModulePermissions = await db.query(`
          SELECT Id FROM SubModulePermissions 
          WHERE SubModuleId = ${subModule.Id} AND IsActive = 1
        `);
        
        for (const smp of subModulePermissions.recordset) {
          // Check if permission already exists
          const existingPermission = await db.query(`
            SELECT Id FROM RolePermissions 
            WHERE RoleId = ${plazaManagerId} AND SubModulePermissionId = ${smp.Id}
          `);
          
          if (existingPermission.recordset.length === 0) {
            await db.query(`
              INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
              VALUES (${plazaManagerId}, ${smp.Id}, 1, 1, GETDATE())
            `);
            console.log(`✓ Added permission for PlazaManager: ${subModule.Name}`);
          }
        }
      }
    }
    
    // Step 4: Populate geographic data
    console.log('\n4. POPULATING GEOGRAPHIC DATA...');
    
    const countryCount = await db.query('SELECT COUNT(*) as Count FROM Country');
    if (countryCount.recordset[0].Count === 0) {
      console.log('Adding sample countries...');
      
      await db.query(`
        INSERT INTO Country (Name, IsActive, CreatedBy, CreatedOn) VALUES
        ('India', 1, 1, GETDATE()),
        ('United States', 1, 1, GETDATE()),
        ('United Kingdom', 1, 1, GETDATE())
      `);
      
      // Add sample states for India
      const indiaId = await db.query('SELECT Id FROM Country WHERE Name = \'India\'');
      if (indiaId.recordset.length > 0) {
        await db.query(`
          INSERT INTO State (Name, CountryId, IsActive, CreatedBy, CreatedOn) VALUES
          ('West Bengal', ${indiaId.recordset[0].Id}, 1, 1, GETDATE()),
          ('Maharashtra', ${indiaId.recordset[0].Id}, 1, 1, GETDATE()),
          ('Karnataka', ${indiaId.recordset[0].Id}, 1, 1, GETDATE()),
          ('Tamil Nadu', ${indiaId.recordset[0].Id}, 1, 1, GETDATE()),
          ('Delhi', ${indiaId.recordset[0].Id}, 1, 1, GETDATE())
        `);
      }
      
      console.log('✓ Geographic data populated');
    } else {
      console.log('Geographic data already exists');
    }
    
    // Step 5: Final verification
    console.log('\n5. FINAL VERIFICATION...');
    
    const finalPermissionCount = await db.query(`
      SELECT 
        r.Name as RoleName,
        COUNT(*) as TotalPermissions
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      WHERE rp.IsActive = 1
      GROUP BY r.Id, r.Name
      ORDER BY r.Id
    `);
    
    console.log('Final permission counts:', finalPermissionCount.recordset);
    
    console.log('\n=== PERMISSION FIXES COMPLETE ===');
    console.log('\nNext steps:');
    console.log('1. Update frontend to use Company view instead of direct table');
    console.log('2. Test CompanyAdmin and PlazaManager access');
    console.log('3. Verify geographic data integration');
    
    process.exit(0);
  } catch (error) {
    console.error('Permission fix failed:', error);
    process.exit(1);
  }
}

fixPermissionIssues();
