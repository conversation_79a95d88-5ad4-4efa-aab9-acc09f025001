import api from '../services/api'; // Shared Axios instance

export const laneFastagConfigApi = {
  /**
   * Fetches the list of all lane Fastag configurations.
   * GET /lane-fastag
   */
  getAllConfigurations: async () => {
    try {
      // Add a cache-busting parameter to prevent caching
      const timestamp = new Date().getTime();
      console.log('Requesting FasTag configurations from API');
      const response = await api.get(`/lane-fastag?_=${timestamp}`);
      
      console.log('FasTag API response:', response.data);
      
      if (!response.data || !response.data.data) {
        console.warn('Invalid response format from FasTag API:', response.data);
        return [];
      }
      
      return response.data.data;
    } catch (error) {
      console.error('Error fetching FasTag configurations:', error);
      throw error;
    }
  },

  /**
   * Fetches the details of a single lane Fastag configuration by its ID.
   * GET /lane-fastag/:id
   */
  getConfigurationById: async (id) => {
    const response = await api.get(`/lane-fastag/${id}`);
    return response.data.data;
  },

  /**
   * Fetches lane Fastag configurations by plaza ID.
   * GET /lane-fastag/plaza/:plazaId
   */
  getConfigurationsByPlaza: async (plazaId) => {
    const response = await api.get(`/lane-fastag/plaza/${plazaId}`);
    return response.data.data;
  },

  /**
   * Creates a new lane Fastag configuration.
   * POST /lane-fastag
   */
  createConfiguration: async (data) => {
    const response = await api.post('/lane-fastag', data);
    return response.data;
  },

  /**
   * Updates a lane Fastag configuration by ID.
   * PUT /lane-fastag/:id
   */
  updateConfiguration: async (id, data) => {
    const response = await api.put(`/lane-fastag/${id}`, data);
    return response.data;
  },

  /**
   * Deletes a lane Fastag configuration by ID.
   * DELETE /lane-fastag/:id
   */
  deleteConfiguration: async (id) => {
    await api.delete(`/lane-fastag/${id}`);
  },

  /**
   * Bulk create or update multiple lane Fastag configurations.
   * POST /lane-fastag/bulk
   */
  bulkCreateOrUpdate: async (data) => {
    const response = await api.post('/lane-fastag/bulk', data);
    return response.data;
  }
};