// <PERSON><PERSON><PERSON> to remove all plaza assignments from accr user except AMBUJA CITY CENTRE RAIPUR
// This will help test the dashboard filtering with a single plaza assignment

const db = require('./backend/src/config/database');

async function removeAccrPlazas() {
  try {
    console.log('=== REMOVING PLAZA ASSIGNMENTS FROM ACCR USER ===\n');
    
    const accrUserId = 5;
    const keepPlazaCode = '102'; // AMBUJA CITY CENTRE RAIPUR
    
    // 1. First, let's see current assignments
    console.log('1. CURRENT PLAZA ASSIGNMENTS FOR ACCR');
    console.log('====================================');
    
    const currentAssignmentsQuery = `
      SELECT 
        up.Id as AssignmentId,
        up.PlazaId,
        p.PlazaName,
        p.PlazaCode,
        c.CompanyName,
        up.IsActive,
        up.CreatedOn
      FROM UserPlaza up
      INNER JOIN Plaza p ON up.PlazaId = p.Id
      INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE up.UserId = @userId
      ORDER BY p.PlazaName
    `;
    
    const currentAssignments = await db.query(currentAssignmentsQuery, { userId: accrUserId });
    
    console.log(`Current assignments for accr user: ${currentAssignments.recordset.length}`);
    currentAssignments.recordset.forEach((assignment, index) => {
      const status = assignment.IsActive ? 'ACTIVE' : 'INACTIVE';
      const keepStatus = assignment.PlazaCode === keepPlazaCode ? ' [KEEP THIS ONE]' : ' [WILL BE REMOVED]';
      console.log(`  ${index + 1}. ${assignment.PlazaName} (${assignment.PlazaCode}) - ${status}${keepStatus}`);
      console.log(`     Company: ${assignment.CompanyName}`);
      console.log(`     Assignment ID: ${assignment.AssignmentId}`);
      console.log(`     Created: ${assignment.CreatedOn}`);
      console.log('');
    });
    
    // 2. Identify assignments to remove
    const assignmentsToRemove = currentAssignments.recordset.filter(a => a.PlazaCode !== keepPlazaCode);
    const assignmentToKeep = currentAssignments.recordset.find(a => a.PlazaCode === keepPlazaCode);
    
    console.log('2. ASSIGNMENTS TO BE MODIFIED');
    console.log('=============================');
    
    if (assignmentToKeep) {
      console.log(`✅ KEEPING: ${assignmentToKeep.PlazaName} (${assignmentToKeep.PlazaCode})`);
      console.log(`   Company: ${assignmentToKeep.CompanyName}`);
      console.log(`   Assignment ID: ${assignmentToKeep.AssignmentId}`);
    } else {
      console.log('❌ ERROR: AMBUJA CITY CENTRE RAIPUR (102) not found in current assignments!');
      console.log('Available plaza codes:', currentAssignments.recordset.map(a => a.PlazaCode).join(', '));
      process.exit(1);
    }
    
    console.log(`\n🗑️  REMOVING ${assignmentsToRemove.length} assignments:`);
    assignmentsToRemove.forEach((assignment, index) => {
      console.log(`  ${index + 1}. ${assignment.PlazaName} (${assignment.PlazaCode})`);
      console.log(`     Assignment ID: ${assignment.AssignmentId}`);
    });
    
    // 3. Confirm before proceeding
    console.log('\n3. CONFIRMATION');
    console.log('===============');
    console.log('This operation will:');
    console.log(`  • Keep 1 plaza assignment: AMBUJA CITY CENTRE RAIPUR (102)`);
    console.log(`  • Remove ${assignmentsToRemove.length} plaza assignments`);
    console.log('  • Set IsActive = 0 for removed assignments (soft delete)');
    console.log('  • Preserve assignment history for audit purposes');
    
    // 4. Execute the removal
    console.log('\n4. EXECUTING REMOVAL');
    console.log('===================');
    
    if (assignmentsToRemove.length === 0) {
      console.log('✅ No assignments to remove. User already has only AMBUJA CITY CENTRE RAIPUR.');
      process.exit(0);
    }
    
    // Execute the removal (without transaction for simplicity)
    let removedCount = 0;
    
    for (const assignment of assignmentsToRemove) {
      const updateQuery = `
        UPDATE UserPlaza 
        SET IsActive = 0, 
            ModifiedOn = GETDATE(),
            ModifiedBy = @userId
        WHERE Id = @assignmentId AND UserId = @userId
      `;
      
      try {
        const result = await db.query(updateQuery, {
          assignmentId: assignment.AssignmentId,
          userId: accrUserId
        });
        
        if (result.rowsAffected[0] > 0) {
          console.log(`✅ Removed: ${assignment.PlazaName} (${assignment.PlazaCode})`);
          removedCount++;
        } else {
          console.log(`❌ Failed to remove: ${assignment.PlazaName} (${assignment.PlazaCode})`);
        }
      } catch (error) {
        console.log(`❌ Error removing ${assignment.PlazaName}: ${error.message}`);
      }
    }
    
    console.log(`\n✅ Successfully removed ${removedCount} plaza assignments`);
    
    // 5. Verify the changes
    console.log('\n5. VERIFICATION');
    console.log('===============');
    
    const verificationQuery = `
      SELECT 
        up.Id as AssignmentId,
        p.PlazaName,
        p.PlazaCode,
        c.CompanyName,
        up.IsActive,
        up.ModifiedOn
      FROM UserPlaza up
      INNER JOIN Plaza p ON up.PlazaId = p.Id
      INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE up.UserId = @userId
      ORDER BY up.IsActive DESC, p.PlazaName
    `;
    
    const verificationResult = await db.query(verificationQuery, { userId: accrUserId });
    
    console.log('Updated assignments for accr user:');
    verificationResult.recordset.forEach((assignment, index) => {
      const status = assignment.IsActive ? 'ACTIVE' : 'INACTIVE';
      const statusColor = assignment.IsActive ? '✅' : '❌';
      console.log(`  ${statusColor} ${assignment.PlazaName} (${assignment.PlazaCode}) - ${status}`);
      console.log(`     Company: ${assignment.CompanyName}`);
      if (assignment.ModifiedOn) {
        console.log(`     Modified: ${assignment.ModifiedOn}`);
      }
      console.log('');
    });
    
    const activeAssignments = verificationResult.recordset.filter(a => a.IsActive);
    console.log(`\n📊 SUMMARY:`);
    console.log(`  • Total assignments: ${verificationResult.recordset.length}`);
    console.log(`  • Active assignments: ${activeAssignments.length}`);
    console.log(`  • Inactive assignments: ${verificationResult.recordset.length - activeAssignments.length}`);
    
    if (activeAssignments.length === 1 && activeAssignments[0].PlazaCode === keepPlazaCode) {
      console.log('\n🎉 SUCCESS: accr user now has only AMBUJA CITY CENTRE RAIPUR (102) assignment!');
    } else {
      console.log('\n⚠️  WARNING: Unexpected result. Please verify manually.');
    }
    
    // 6. Test dashboard access
    console.log('\n6. TESTING DASHBOARD ACCESS');
    console.log('==========================');
    
    const dashboardTestQuery = `
      SELECT 
        p.PlazaCode,
        p.PlazaName,
        COUNT(t.PakringDataID) as TransactionCount
      FROM Plaza p 
      JOIN UserPlaza up ON p.Id = up.PlazaId 
      LEFT JOIN tblParkwiz_Parking_Data t ON p.PlazaCode = t.PlazaCode
      WHERE up.UserId = @userId AND up.IsActive = 1
      GROUP BY p.PlazaCode, p.PlazaName
      ORDER BY p.PlazaName
    `;
    
    const dashboardTest = await db.query(dashboardTestQuery, { userId: accrUserId });
    
    console.log('Dashboard data now accessible to accr user:');
    if (dashboardTest.recordset.length > 0) {
      dashboardTest.recordset.forEach(data => {
        console.log(`  ✅ ${data.PlazaName} (${data.PlazaCode}): ${data.TransactionCount} transactions`);
      });
    } else {
      console.log('  ❌ No plaza data accessible (this might be expected if no transactions exist)');
    }
    
    console.log('\n=== OPERATION COMPLETE ===');
    console.log('\nNext steps:');
    console.log('1. Test the dashboard to verify filtering works with single plaza');
    console.log('2. Run: cd d:/PWVMS/backend && node ../test-dashboard-filtering.js');
    console.log('3. Check user can only see AMBUJA CITY CENTRE RAIPUR data');
    
    process.exit(0);
    
  } catch (error) {
    console.error('Error during plaza removal:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

removeAccrPlazas();