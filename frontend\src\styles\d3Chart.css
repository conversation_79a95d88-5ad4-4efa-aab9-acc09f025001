/* D3 Chart Styles */

/* Tooltip styles */
.d3-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
  max-width: 200px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Dark theme adjustments */
html[data-theme='dark'] .d3-tooltip {
  background: rgba(255, 255, 255, 0.9);
  color: #1f2937;
}

/* Parkwiz theme adjustments */
html[data-theme='parkwiz'] .d3-tooltip {
  background: rgba(85, 85, 85, 0.9);
  color: #ffffff;
}

/* Saffron theme adjustments */
html[data-theme='saffron'] .d3-tooltip {
  background: rgba(120, 53, 15, 0.9);
  color: #fffbeb;
}

/* Axis styles */
.axis path,
.axis line {
  stroke: #e5e7eb;
}

html[data-theme='dark'] .axis path,
html[data-theme='dark'] .axis line {
  stroke: #4b5563;
}

html[data-theme='parkwiz'] .axis path,
html[data-theme='parkwiz'] .axis line {
  stroke: #dee2e6;
}

html[data-theme='saffron'] .axis path,
html[data-theme='saffron'] .axis line {
  stroke: #d97706;
}

/* Grid lines */
.grid line {
  stroke: #e5e7eb;
}

html[data-theme='dark'] .grid line {
  stroke: #4b5563;
}

html[data-theme='parkwiz'] .grid line {
  stroke: #dee2e6;
}

html[data-theme='saffron'] .grid line {
  stroke: #d97706;
}

/* Animation for data points */
.dot {
  transition: r 0.2s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .d3-tooltip {
    font-size: 10px;
    padding: 8px;
  }
}