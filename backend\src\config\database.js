// backend/src/config/database.js - MODIFIED VERSION

const sql = require('mssql');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    enableArithAbort: true,
    connectionTimeout: 60000,    // Increased to 60 seconds
    requestTimeout: 60000,       // Increased to 60 seconds
    // Explicitly disable DAC usage
    admin: false
  },
  pool: {
    max: 20,                    // Increased pool size for better concurrency
    min: 5,                     // Keep minimum connections ready
    idleTimeoutMillis: 60000    // Increased idle timeout
  }
};

// Create a connection pool
let pool = null;

// Track if we're currently initializing to prevent multiple concurrent initializations
let isInitializing = false;

// Initialize the connection pool
const initializePool = async () => {
  // Prevent multiple concurrent initializations
  if (isInitializing) {
    console.log('Pool initialization already in progress, waiting...');
    while (isInitializing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    return pool;
  }

  isInitializing = true;

  try {
    if (pool) {
      console.log('Closing existing connection pool');
      try {
        await pool.close();
        console.log('Existing connection pool closed successfully');
      } catch (closeError) {
        console.error('Error closing existing pool (continuing anyway):', closeError.message);
        // Continue even if close fails
      }
    }
    
    console.log('Creating new connection pool with config:', {
      server: dbConfig.server,
      database: dbConfig.database,
      port: dbConfig.port,
      user: dbConfig.user,
      // Not logging password for security
      options: {
        ...dbConfig.options,
        // Not showing full options for brevity
      }
    });
    
    pool = await sql.connect(dbConfig);
    console.log('Database connection pool established');
    
    // Get database info with a simple query to test connection
    const result = await pool.request().query(`
      SELECT 
        DB_NAME() as DatabaseName,
        @@CONNECTIONS as TotalConnections,
        @@MAX_CONNECTIONS as MaxConnections
    `);
    
    if (result.recordset.length > 0) {
      const { DatabaseName, TotalConnections, MaxConnections } = result.recordset[0];
      console.log(`Connected to database: ${DatabaseName}`);
      console.log(`Total connections: ${TotalConnections}`);
      console.log(`Max connections: ${MaxConnections}`);
      console.log('Database connected successfully');
    }
    
    return pool;
  } catch (error) {
    console.error('SQL Server connection pool error:', error);
    throw error;
  } finally {
    isInitializing = false;
  }
};

// Execute a query with retry logic
const query = async (queryText, params = {}, maxRetries = 3) => {
  // If pool doesn't exist and we're not already initializing, initialize it
  if (!pool && !isInitializing) {
    await initializePool();
  }

  // If we're currently initializing, wait for it to complete
  while (isInitializing) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  let retries = maxRetries;

  while (retries >= 0) {
    try {
      // Check if pool exists before using it
      if (!pool) {
        throw new Error('Database connection pool not available');
      }

      const request = pool.request();

      // Add parameters to the request
      Object.entries(params).forEach(([key, value]) => {
        request.input(key, value);
      });

      // Execute the query
      const result = await request.query(queryText);
      return result;
    } catch (error) {
      console.error('Query execution error:', error.message);

      // Log the specific error type for debugging
      console.log(`Database error type: ${error.code}, message: ${error.message}`);

      // If we have retries left, try again
      if (retries > 0) {
        console.log(`Retrying query, ${retries} attempts left`);
        retries--;

        // For connection errors, try to reinitialize the pool (but only once per retry cycle)
        if ((error.code === 'ELOGIN' || error.code === 'ESOCKET' || error.code === 'ECONNCLOSED') && !isInitializing) {
          console.log('Attempting to reinitialize connection pool due to connection error');
          try {
            await initializePool();
          } catch (initError) {
            console.error('Failed to reinitialize connection pool:', initError.message);
            // Continue to next retry without reinitializing again
          }
        }

        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        // No more retries, throw the error
        throw error;
      }
    }
  }
};

// Close the connection pool
const closePool = async () => {
  if (pool) {
    try {
      await pool.close();
      pool = null;
      console.log('Database connection pool closed');
    } catch (error) {
      console.error('Error closing database connection pool:', error);
      throw error;
    }
  }
};

// Initialize the pool when the module is imported
initializePool().catch(error => {
  console.error('Failed to initialize database connection pool:', error);
  process.exit(1); // Exit the process if initial connection fails
});

// Handle process termination
process.on('SIGINT', async () => {
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closePool();
  process.exit(0);
});

module.exports = {
  query,
  closePool,
  sql
};
