const db = require('./src/config/database');

async function fixCompanyAdminFinal() {
  try {
    console.log('=== FIXING COMPANY ADMIN ACCESS - FINAL ===');
    
    // Step 1: Check current company assignments
    console.log('\n1. CURRENT COMPANY ASSIGNMENTS...');
    
    const currentAssignments = await db.query(`
      SELECT uc.CompanyId, c.CompanyName, uc.IsActive
      FROM UserCompany uc
      JOIN Company c ON uc.CompanyId = c.Id
      WHERE uc.UserId = 4
    `);
    
    console.log('Current assignments:');
    currentAssignments.recordset.forEach(assignment => {
      console.log(`  ${assignment.IsActive ? '✓' : '✗'} ${assignment.CompanyName} (ID: ${assignment.CompanyId})`);
    });
    
    // Step 2: Find companies with plazas
    console.log('\n2. COMPANIES WITH PLAZAS...');
    
    const companiesWithPlazas = await db.query(`
      SELECT 
        c.Id,
        c.Company<PERSON>ame,
        COUNT(p.Id) as PlazaCount
      FROM Company c
      JOIN Plaza p ON c.Id = p.CompanyId
      WHERE c.IsActive = 1 AND p.IsActive = 1
      GROUP BY c.Id, c.CompanyName
      ORDER BY c.CompanyName
    `);
    
    console.log('Companies with plazas:');
    companiesWithPlazas.recordset.forEach(company => {
      console.log(`  ${company.CompanyName} (ID: ${company.Id}) - ${company.PlazaCount} plazas`);
    });
    
    // Step 3: Assign CompanyAdmin to companies with plazas
    console.log('\n3. ASSIGNING TO COMPANIES WITH PLAZAS...');
    
    for (const company of companiesWithPlazas.recordset) {
      // Check if assignment already exists
      const existingAssignment = await db.query(`
        SELECT Id, IsActive FROM UserCompany 
        WHERE UserId = 4 AND CompanyId = ${company.Id}
      `);
      
      if (existingAssignment.recordset.length === 0) {
        // Create new assignment
        await db.query(`
          INSERT INTO UserCompany (UserId, CompanyId, IsActive, CreatedBy, CreatedOn)
          VALUES (4, ${company.Id}, 1, 1, GETDATE())
        `);
        console.log(`  ✓ Added: CompanyAdmin -> ${company.CompanyName}`);
      } else {
        // Activate existing assignment
        await db.query(`
          UPDATE UserCompany 
          SET IsActive = 1, ModifiedBy = 1, ModifiedOn = GETDATE()
          WHERE UserId = 4 AND CompanyId = ${company.Id}
        `);
        console.log(`  ✓ Activated: CompanyAdmin -> ${company.CompanyName}`);
      }
    }
    
    // Step 4: Verify accessible data
    console.log('\n4. VERIFYING ACCESSIBLE DATA...');
    
    // Companies
    const accessibleCompanies = await db.query(`
      SELECT c.Id, c.CompanyName, c.CountryName, c.StateName
      FROM Company c
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND c.IsActive = 1
      ORDER BY c.CompanyName
    `);
    
    console.log(`Accessible companies: ${accessibleCompanies.recordset.length}`);
    accessibleCompanies.recordset.forEach(company => {
      console.log(`  ✓ ${company.CompanyName} (${company.CountryName}, ${company.StateName})`);
    });
    
    // Plazas
    const accessiblePlazas = await db.query(`
      SELECT p.Id, p.PlazaName, c.CompanyName
      FROM Plaza p
      JOIN Company c ON p.CompanyId = c.Id
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND p.IsActive = 1
      ORDER BY c.CompanyName, p.PlazaName
    `);
    
    console.log(`Accessible plazas: ${accessiblePlazas.recordset.length}`);
    accessiblePlazas.recordset.forEach(plaza => {
      console.log(`  ✓ ${plaza.PlazaName} (${plaza.CompanyName})`);
    });
    
    // Lanes (using correct table name)
    const accessibleLanes = await db.query(`
      SELECT l.Id, l.LaneName, p.PlazaName, c.CompanyName
      FROM tblLaneDetails l
      JOIN Plaza p ON l.PlazaId = p.Id
      JOIN Company c ON p.CompanyId = c.Id
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND l.IsActive = 1
      ORDER BY c.CompanyName, p.PlazaName, l.LaneName
    `);
    
    console.log(`Accessible lanes: ${accessibleLanes.recordset.length}`);
    accessibleLanes.recordset.forEach(lane => {
      console.log(`  ✓ ${lane.LaneName} (${lane.PlazaName} - ${lane.CompanyName})`);
    });
    
    // Users (should include users assigned to same companies/plazas, excluding SuperAdmin)
    const accessibleUsers = await db.query(`
      SELECT DISTINCT u.Id, u.Username, u.Email, r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.Id IN (
        -- Users assigned to same companies
        SELECT DISTINCT uc2.UserId
        FROM UserCompany uc1
        JOIN UserCompany uc2 ON uc1.CompanyId = uc2.CompanyId
        WHERE uc1.UserId = 4 AND uc1.IsActive = 1 AND uc2.IsActive = 1
        
        UNION
        
        -- Users assigned to plazas within the companies
        SELECT DISTINCT up.UserId
        FROM UserCompany uc
        JOIN Plaza p ON uc.CompanyId = p.CompanyId
        JOIN UserPlaza up ON p.Id = up.PlazaId
        WHERE uc.UserId = 4 AND uc.IsActive = 1 AND up.IsActive = 1
      )
      AND u.IsActive = 1
      AND r.Name != 'SuperAdmin'  -- Exclude SuperAdmin users
      AND u.Id != 4  -- Exclude themselves
      ORDER BY r.Name, u.Username
    `);
    
    console.log(`Accessible users (excluding SuperAdmin and self): ${accessibleUsers.recordset.length}`);
    accessibleUsers.recordset.forEach(user => {
      console.log(`  ✓ ${user.Username} (${user.Email}) - ${user.RoleName}`);
    });
    
    // Step 5: Check user creation issue
    console.log('\n5. CHECKING USER CREATION PERMISSIONS...');
    
    const userCreationPerms = await db.query(`
      SELECT 
        m.Name as ModuleName,
        sm.Name as SubModuleName,
        p.Name as PermissionName
      FROM RolePermissions rp
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE rp.RoleId = (SELECT RoleId FROM Users WHERE Id = 4)
      AND m.Name = 'User Management'
      AND rp.IsActive = 1
      ORDER BY sm.Name, p.Name
    `);
    
    console.log('User Management permissions for CompanyAdmin:');
    userCreationPerms.recordset.forEach(perm => {
      console.log(`  ✓ ${perm.SubModuleName} -> ${perm.PermissionName}`);
    });
    
    // Check what roles they can see
    const roleAccess = await db.query(`
      SELECT r.Id, r.Name, r.Description
      FROM Roles r
      WHERE r.Name != 'SuperAdmin'  -- Should not see SuperAdmin role
      ORDER BY r.Name
    `);
    
    console.log('\nRoles that should be available to CompanyAdmin:');
    roleAccess.recordset.forEach(role => {
      console.log(`  ✓ ${role.Name} - ${role.Description}`);
    });
    
    console.log('\n=== COMPANY ADMIN ACCESS FIXED ===');
    console.log('\n✅ DATA ACCESS FIXED:');
    console.log(`• Companies: ${accessibleCompanies.recordset.length} accessible`);
    console.log(`• Plazas: ${accessiblePlazas.recordset.length} accessible`);
    console.log(`• Lanes: ${accessibleLanes.recordset.length} accessible`);
    console.log(`• Users: ${accessibleUsers.recordset.length} accessible (excluding SuperAdmin)`);
    
    console.log('\n🔧 BACKEND FIXES STILL NEEDED:');
    console.log('1. User creation should be restricted to non-SuperAdmin roles');
    console.log('2. User list should exclude SuperAdmin users');
    console.log('3. CompanyAdmin should not be able to edit themselves');
    console.log('4. Role dropdown should exclude SuperAdmin role');
    
    console.log('\n📋 TEST NOW:');
    console.log('Login as CompanyAdmin (company admin / PASSWORD) and verify:');
    console.log('- Companies section shows assigned companies');
    console.log('- Plaza Management shows plazas from assigned companies');
    console.log('- Lane Management shows lanes from assigned plazas');
    console.log('- User Management shows users (excluding SuperAdmin)');
    
    process.exit(0);
  } catch (error) {
    console.error('Fix failed:', error);
    process.exit(1);
  }
}

fixCompanyAdminFinal();
