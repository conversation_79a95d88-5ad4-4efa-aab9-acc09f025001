# Prepare PWVMS Deployment Package
# This script creates a production-ready deployment package

# Configuration
$sourceDir = $PSScriptRoot  # Current directory where the script is located
$packageDir = "$sourceDir\deployment-package"
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$zipFileName = "PWVMS-Deployment-$timestamp.zip"

Write-Host "Preparing PWVMS deployment package..." -ForegroundColor Yellow

# Create package directory
if (Test-Path $packageDir) {
    Remove-Item -Path $packageDir -Recurse -Force
}
New-Item -ItemType Directory -Path $packageDir -Force | Out-Null

# Create necessary subdirectories
$deploymentDirs = @(
    "$packageDir\backend",
    "$packageDir\backend\src",
    "$packageDir\backend\Uploads",
    "$packageDir\backend\Uploads\Companies",
    "$packageDir\backend\Uploads\Plazas",
    "$packageDir\frontend\build",
    "$packageDir\docs"
)

foreach ($dir in $deploymentDirs) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
}

# Build the React frontend
Write-Host "Building React frontend..." -ForegroundColor Yellow
Set-Location -Path "$sourceDir\frontend"
npm install
npm run build
Set-Location -Path $sourceDir

# Copy frontend build files
Write-Host "Copying frontend build files..." -ForegroundColor Yellow
Copy-Item -Path "$sourceDir\frontend\build\*" -Destination "$packageDir\frontend\build" -Recurse -Force

# Copy backend files
Write-Host "Copying backend files..." -ForegroundColor Yellow
Copy-Item -Path "$sourceDir\backend\src\*" -Destination "$packageDir\backend\src" -Recurse -Force
Copy-Item -Path "$sourceDir\backend\package.json" -Destination "$packageDir\backend\package.json" -Force
Copy-Item -Path "$sourceDir\backend\package-lock.json" -Destination "$packageDir\backend\package-lock.json" -Force -ErrorAction SilentlyContinue

# Install backend production dependencies
Write-Host "Installing backend production dependencies..." -ForegroundColor Yellow
Set-Location -Path "$packageDir\backend"
npm install --production
Set-Location -Path $sourceDir

# Remove node_modules/.cache to reduce size (optional)
if (Test-Path "$packageDir\backend\node_modules\.cache") {
    Remove-Item -Path "$packageDir\backend\node_modules\.cache" -Recurse -Force
}

# Copy environment template
Write-Host "Copying environment template..." -ForegroundColor Yellow
if (Test-Path "$sourceDir\backend\.env.production") {
    Copy-Item -Path "$sourceDir\backend\.env.production" -Destination "$packageDir\backend\.env.template" -Force
} else {
    Copy-Item -Path "$sourceDir\backend\.env" -Destination "$packageDir\backend\.env.template" -Force
}

# Copy web.config
Write-Host "Copying web.config..." -ForegroundColor Yellow
Copy-Item -Path "$sourceDir\web.config" -Destination "$packageDir\web.config" -Force

# Copy deployment scripts
Write-Host "Copying deployment scripts..." -ForegroundColor Yellow
Copy-Item -Path "$sourceDir\deploy-to-iis.ps1" -Destination "$packageDir\deploy-to-iis.ps1" -Force
Copy-Item -Path "$sourceDir\install-iisnode.ps1" -Destination "$packageDir\install-iisnode.ps1" -Force

# Create a simple deployment guide
$deploymentGuideContent = @"
# PWVMS Deployment Guide

This package contains a production-ready version of the PWVMS application.

## Prerequisites

1. Windows Server with IIS installed
2. URL Rewrite Module for IIS
3. iisnode v0.2.26
4. Node.js (LTS version recommended)
5. SQL Server database

## Deployment Steps

1. Extract this package to a temporary location (e.g., D:\PWVMS-Deployment)

2. Install prerequisites:
   - Run PowerShell as Administrator
   - Execute: .\install-iisnode.ps1

3. Configure the environment:
   - Navigate to the backend directory
   - Rename .env.template to .env
   - Edit .env and update the following:
     - DB_USER, DB_PASSWORD, DB_SERVER, DB_NAME (database credentials)
     - JWT_SECRET (set a strong, unique secret key)
     - FRONTEND_URL (set to your production URL)

4. Deploy to IIS:
   - Run PowerShell as Administrator
   - Execute: .\deploy-to-iis.ps1

5. Verify the deployment:
   - Open a browser and navigate to your server's IP or hostname
   - Test the application functionality

## Troubleshooting

- Check IIS logs: C:\inetpub\logs\LogFiles\W3SVC1\
- Check iisnode logs: C:\inetpub\wwwroot\PWVMS\iisnode\
- Verify database connection settings in .env file
- Ensure all IIS components are properly installed

## Support

For additional support, please contact the development team.
"@

Set-Content -Path "$packageDir\DEPLOYMENT-GUIDE.md" -Value $deploymentGuideContent

# Create a detailed README
$readmeContent = @"
# PWVMS - Parking and Toll Management System

## Package Contents

This deployment package contains:

- \`frontend/build/\` - Production-ready React frontend
- \`backend/\` - Node.js Express backend with dependencies
- \`web.config\` - IIS configuration file
- \`deploy-to-iis.ps1\` - Deployment script
- \`install-iisnode.ps1\` - Prerequisites installation script
- \`DEPLOYMENT-GUIDE.md\` - Step-by-step deployment instructions

## System Requirements

- Windows Server 2016/2019/2022 or Windows 10/11
- IIS 10.0 or later
- Node.js LTS version
- SQL Server database
- 4GB RAM minimum (8GB recommended)
- 10GB free disk space

## Application Architecture

- Frontend: React.js
- Backend: Node.js with Express
- Database: Microsoft SQL Server
- Authentication: JWT-based

## Configuration

The application requires configuration in the \`.env\` file located in the \`backend\` directory.
See \`DEPLOYMENT-GUIDE.md\` for detailed configuration instructions.

## Post-Deployment Steps

After deployment, you should:

1. Create an administrator account
2. Configure company settings
3. Set up plazas and lanes
4. Configure security settings

## Maintenance

Regular maintenance tasks:

1. Database backups
2. Log rotation
3. Security updates

## Support

For technical support, please contact:
- Email: <EMAIL>
- Phone: (*************
"@

Set-Content -Path "$packageDir\README.md" -Value $readmeContent

# Copy documentation
Write-Host "Copying documentation..." -ForegroundColor Yellow
Copy-Item -Path "$sourceDir\docs\*" -Destination "$packageDir\docs" -Recurse -Force

# Create a zip file of the deployment package
Write-Host "Creating deployment package zip file..." -ForegroundColor Yellow
Compress-Archive -Path "$packageDir\*" -DestinationPath "$sourceDir\$zipFileName" -Force

Write-Host "Deployment package created successfully!" -ForegroundColor Green
Write-Host "Package location: $sourceDir\$zipFileName" -ForegroundColor Green
Write-Host "Extracted package: $packageDir" -ForegroundColor Green