// frontend/src/contexts/authContext.js
import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import api, { checkAuth } from '../services/api';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState(null);
  const refreshTimerRef = useRef(null);

  // Function to set up periodic token validation
  const setupTokenRefresh = () => {
    // Clear any existing timer
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
    }
    
    // Set up a new timer to check auth status every 5 minutes
    refreshTimerRef.current = setInterval(async () => {
      const isAuthenticated = await checkAuth();
      if (!isAuthenticated) {
        console.log('Auth check failed, logging out');
        logout();
      }
    }, 5 * 60 * 1000); // 5 minutes
  };

  useEffect(() => {
    // Check if user is logged in on page load
    const token = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');

    if (token && storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
        console.log('Loaded user from localStorage:', userData);

        // Refresh user data from server
        refreshUserData(token).catch(err => {
          console.error('Failed to refresh user data:', err);
          // If token is invalid, log the user out
          if (err.response && err.response.status === 401) {
            console.log('Token invalid, logging out');
            logout();
          }
        });
        
        // Set up token refresh
        setupTokenRefresh();
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        logout();
      }
    } else {
      console.log('No token or user found in localStorage');
    }

    setLoading(false);
    
    // Cleanup function to clear the interval when component unmounts
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, []);

  // Refresh user data from the server
  const refreshUserData = async (token) => {
    try {
      const response = await api.get('/auth/me');
      console.log('Refresh user data response:', response.data);

      // The response structure is { success, message, data: { ... user data ... } }
      if (response.data && response.data.data) {
        const userData = response.data.data;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        return userData;
      }
      return null;
    } catch (error) {
      console.error('Error refreshing user data:', error);
      throw error;
    }
  };

  const login = async (credentials) => {
    setLoading(true);
    setAuthError(null);

    try {
      const response = await api.post('/auth/login', credentials);

      // Check the structure of the response data
      console.log('Login response:', response.data);

      // The response structure is { success, message, data: { token, user } }
      if (!response.data || !response.data.data || !response.data.data.token || !response.data.data.user) {
        console.error('Invalid response format:', response.data);
        throw new Error('Invalid response format from server');
      }

      // Extract token and user data from the nested structure
      const { token, user } = response.data.data;

      setUser(user);
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
      
      // Set up token refresh
      setupTokenRefresh();
      
      setLoading(false);
      return user;
    } catch (error) {
      setLoading(false);
      console.error('Login error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Login failed';
      setAuthError(errorMessage);
      throw error;
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // Clear the refresh timer
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
      refreshTimerRef.current = null;
    }
    
    // Redirect to login page if not already there
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    if (!user) return false;
    return user.role === role;
  };

  // Check if user is a SuperAdmin
  const isSuperAdmin = () => hasRole('SuperAdmin');

  // Check if user is a CompanyAdmin
  const isCompanyAdmin = () => hasRole('CompanyAdmin');

  // Check if user is a PlazaManager
  const isPlazaManager = () => hasRole('PlazaManager');

  // Check if user has a specific permission for a module
  const hasPermission = (module, permission) => {
    if (!user || !user.permissions) return false;

    // SuperAdmin has all permissions
    if (user.role === 'SuperAdmin') return true;

    // Check if the user has the specific permission for the module
    return user.permissions[module]?.includes(permission) || false;
  };

  // Check if user has access to a specific company
  const hasCompanyAccess = (companyId) => {
    if (!user) return false;

    // SuperAdmin has access to all companies
    if (user.role === 'SuperAdmin') return true;

    // Check if the user has access to the specific company
    return user.companies?.some(company => company.Id === parseInt(companyId)) || false;
  };

  // Check if user has access to a specific plaza
  const hasPlazaAccess = (plazaId) => {
    if (!user) return false;

    // SuperAdmin has access to all plazas
    if (user.role === 'SuperAdmin') return true;

    // For CompanyAdmin, check if the plaza belongs to their company
    if (user.role === 'CompanyAdmin') {
      // If no specific plazaId is provided, CompanyAdmin has general plaza access
      if (!plazaId) return true;
      
      const plaza = user.plazas?.find(plaza => plaza.Id === parseInt(plazaId));
      return plaza && hasCompanyAccess(plaza.CompanyId);
    }

    // For PlazaManager, check if they have direct access to the plaza
    return user.plazas?.some(plaza => plaza.Id === parseInt(plazaId)) || false;
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      loading,
      authError,
      hasRole,
      isSuperAdmin,
      isCompanyAdmin,
      isPlazaManager,
      hasPermission,
      hasCompanyAccess,
      hasPlazaAccess,
      refreshUserData
    }}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
