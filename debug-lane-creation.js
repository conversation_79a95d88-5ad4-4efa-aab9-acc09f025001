// Debug script to test lane creation and see validation errors
const axios = require('axios');

async function debugLaneCreation() {
  console.log('=== DEBUGGING LANE CREATION ===\n');
  
  const baseURL = 'http://localhost:5000';
  
  // Test with a simple valid case first
  const testData = {
    PlazaID: 13, // AMBUJA CITY CENTRE RAIPUR
    CompanyID: 12,
    LaneNumber: '01',
    LaneType: 'Entry',
    LaneDetails: 'Test Lane',
    TypeCode: '01',
    VehicleType: 'Car',
    UpdatedBy: 'test'
  };
  
  console.log('Testing with data:', JSON.stringify(testData, null, 2));
  
  try {
    const response = await axios.post(`${baseURL}/api/lanes`, testData, {
      headers: {
        'Content-Type': 'application/json',
        // You might need to add authentication headers here
        // 'Authorization': 'Bearer your-jwt-token'
      }
    });
    
    console.log('✅ SUCCESS: Lane created successfully');
    console.log('Response:', response.data);
    
  } catch (error) {
    if (error.response) {
      console.log('❌ ERROR: Got response from server');
      console.log('Status:', error.response.status);
      console.log('Status Text:', error.response.statusText);
      console.log('Response Data:', JSON.stringify(error.response.data, null, 2));
      
      // Check if it's our validation error
      if (error.response.data.validationErrors) {
        console.log('\n🔍 VALIDATION ERRORS DETECTED:');
        error.response.data.validationErrors.forEach((err, index) => {
          console.log(`${index + 1}. Field: ${err.field}`);
          console.log(`   Error: ${err.error}`);
          console.log(`   Max Length: ${err.maxLength}`);
          console.log(`   Current Value: "${err.currentValue}"`);
          console.log('');
        });
      }
      
    } else if (error.request) {
      console.log('❌ NETWORK ERROR: No response received');
      console.log('Request:', error.request);
    } else {
      console.log('❌ ERROR:', error.message);
    }
  }
  
  // Test with problematic data that should trigger validation
  console.log('\n=== TESTING VALIDATION ERRORS ===\n');
  
  const invalidData = {
    PlazaID: 13,
    CompanyID: 12,
    LaneNumber: '123', // Too long - should fail
    LaneType: 'Entry',
    LaneDetails: 'Test Lane',
    TypeCode: '001', // Too long - should fail
    VehicleType: 'Car',
    UpdatedBy: 'administrator' // Too long - should fail
  };
  
  console.log('Testing with invalid data:', JSON.stringify(invalidData, null, 2));
  
  try {
    const response = await axios.post(`${baseURL}/api/lanes`, invalidData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('⚠️ UNEXPECTED: Request succeeded when it should have failed');
    console.log('Response:', response.data);
    
  } catch (error) {
    if (error.response) {
      console.log('✅ EXPECTED: Validation errors caught');
      console.log('Status:', error.response.status);
      console.log('Response Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.data.validationErrors) {
        console.log('\n🔍 VALIDATION ERRORS:');
        error.response.data.validationErrors.forEach((err, index) => {
          console.log(`${index + 1}. ${err.field}: ${err.error}`);
        });
      }
    } else {
      console.log('❌ NETWORK ERROR:', error.message);
    }
  }
}

// Check if server is running first
async function checkServer() {
  try {
    // Try to get lanes instead of health endpoint
    const response = await axios.get('http://localhost:5000/api/lanes', { timeout: 5000 });
    console.log('✅ Server is running');
    return true;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Server is running (authentication required)');
      return true;
    }
    console.log('❌ Server is not running or not accessible');
    console.log('Error:', error.message);
    console.log('Make sure your backend server is running on http://localhost:5000');
    return false;
  }
}

async function main() {
  console.log('Lane Creation Debug Tool');
  console.log('=======================\n');
  
  const serverRunning = await checkServer();
  if (serverRunning) {
    await debugLaneCreation();
  }
}

main();