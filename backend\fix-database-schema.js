const db = require('./src/config/database');

async function fixDatabaseSchema() {
  try {
    console.log('=== FIXING DATABASE SCHEMA ===');
    
    // Step 1: Check and fix Company table naming
    console.log('\n1. CHECKING COMPANY TABLE...');
    
    const companyTableExists = await db.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'Company' AND TABLE_TYPE = 'BASE TABLE'
    `);
    
    const tblCompanyMasterExists = await db.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'tblCompanyMaster' AND TABLE_TYPE = 'BASE TABLE'
    `);
    
    if (companyTableExists.recordset.length === 0 && tblCompanyMasterExists.recordset.length > 0) {
      console.log('Creating Company view/alias for tblCompanyMaster...');
      
      // Check tblCompanyMaster structure
      const tblCompanyStructure = await db.query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'tblCompanyMaster'
        ORDER BY ORDINAL_POSITION
      `);
      console.log('tblCompanyMaster structure:', tblCompanyStructure.recordset);
      
      // Create a view to map tblCompanyMaster to Company
      try {
        await db.query(`
          CREATE VIEW Company AS
          SELECT 
            Id,
            CompanyName,
            ContactPerson,
            ContactNumber,
            ContactEmail,
            GstNumber,
            IsActive,
            CreatedBy,
            CreatedOn,
            ModifiedBy,
            ModifiedOn
          FROM tblCompanyMaster
        `);
        console.log('✓ Company view created successfully');
      } catch (viewError) {
        if (viewError.message.includes('already exists')) {
          console.log('✓ Company view already exists');
        } else {
          console.error('Error creating Company view:', viewError.message);
        }
      }
    }
    
    // Step 2: Check and fix permission system
    console.log('\n2. CHECKING PERMISSION SYSTEM...');
    
    // Check if we need to create proper permission tables
    const rolePermissionsExists = await db.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'RolePermissions' AND TABLE_TYPE = 'BASE TABLE'
    `);
    
    const subModulePermissionsExists = await db.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'SubModulePermissions' AND TABLE_TYPE = 'BASE TABLE'
    `);
    
    console.log('RolePermissions table exists:', rolePermissionsExists.recordset.length > 0);
    console.log('SubModulePermissions table exists:', subModulePermissionsExists.recordset.length > 0);
    
    if (rolePermissionsExists.recordset.length > 0) {
      // Check RolePermissions structure
      const rolePermissionsStructure = await db.query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'RolePermissions'
        ORDER BY ORDINAL_POSITION
      `);
      console.log('RolePermissions structure:', rolePermissionsStructure.recordset);
      
      // Check sample data
      const sampleRolePermissions = await db.query('SELECT TOP 5 * FROM RolePermissions');
      console.log('Sample RolePermissions:', sampleRolePermissions.recordset);
    }
    
    if (subModulePermissionsExists.recordset.length > 0) {
      // Check SubModulePermissions structure
      const subModulePermissionsStructure = await db.query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'SubModulePermissions'
        ORDER BY ORDINAL_POSITION
      `);
      console.log('SubModulePermissions structure:', subModulePermissionsStructure.recordset);
      
      // Check sample data
      const sampleSubModulePermissions = await db.query('SELECT TOP 5 * FROM SubModulePermissions');
      console.log('Sample SubModulePermissions:', sampleSubModulePermissions.recordset);
    }
    
    // Step 3: Check geographic data
    console.log('\n3. CHECKING GEOGRAPHIC DATA...');
    
    const countryCount = await db.query('SELECT COUNT(*) as Count FROM Country');
    const stateCount = await db.query('SELECT COUNT(*) as Count FROM State');
    
    console.log('Countries in database:', countryCount.recordset[0].Count);
    console.log('States in database:', stateCount.recordset[0].Count);
    
    if (countryCount.recordset[0].Count === 0) {
      console.log('No countries found - need to populate geographic data');
    }
    
    // Step 4: Check company-geographic relationship
    console.log('\n4. CHECKING COMPANY-GEOGRAPHIC RELATIONSHIP...');
    
    // Check if tblCompanyMaster has country/state fields
    const companyGeoFields = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblCompanyMaster'
      AND (COLUMN_NAME LIKE '%Country%' OR COLUMN_NAME LIKE '%State%' OR COLUMN_NAME LIKE '%City%')
      ORDER BY ORDINAL_POSITION
    `);
    console.log('Company geographic fields:', companyGeoFields.recordset);
    
    // Step 5: Sample company data
    console.log('\n5. SAMPLE COMPANY DATA...');
    const sampleCompanies = await db.query('SELECT TOP 3 * FROM tblCompanyMaster');
    console.log('Sample companies:', sampleCompanies.recordset);
    
    console.log('\n=== SCHEMA ANALYSIS COMPLETE ===');
    console.log('\nNext steps needed:');
    console.log('1. Fix permission system - create proper role-permission mappings');
    console.log('2. Populate geographic data (countries, states)');
    console.log('3. Add geographic fields to company table if missing');
    console.log('4. Update frontend to use correct table names');
    
    process.exit(0);
  } catch (error) {
    console.error('Schema fix failed:', error);
    process.exit(1);
  }
}

fixDatabaseSchema();
