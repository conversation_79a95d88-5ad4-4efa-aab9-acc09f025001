/**
 * ============================================================================
 * # LANE FASTAG CONFIGURATION CONTROLLER
 * ============================================================================
 * This controller handles all CRUD operations for the tblLaneFastagConfiguration table,
 * which manages Fastag configurations for toll plaza lanes. It provides endpoints to:
 * - Retrieve all or specific lane configurations
 * - Create new lane configurations
 * - Update existing lane configurations
 * - Delete lane configurations
 * - Perform bulk operations on multiple configurations
 *
 * @module controllers/laneFastagConfigurationController
 * @requires mssql
 * @requires ../config/database
 * ============================================================================
 */

const db = require('../config/database');
const sql = require('mssql');
const { responseHandler } = require('../Utils/ResponseHandler');
const { hasPermission } = require('../Utils/PermissionHelper');

/**
 * ============================================================================
 * # DATA ACCESS METHODS
 * ============================================================================
 * Core methods for retrieving lane Fastag configuration data
 */

// Diagnostic endpoint to check user company associations and FasTag configurations
exports.diagnosticCheck = async (req, res) => {
  try {
    const user = req.user;
    
    // Get user's company associations
    const userCompanyQuery = `
      SELECT uc.CompanyId, c.CompanyName, uc.IsActive
      FROM UserCompany uc
      LEFT JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
      WHERE uc.UserId = @userId
    `;
    const userCompanyResult = await db.query(userCompanyQuery, { userId: user.id });
    
    // Get count of FasTag configurations per company
    const fastagCountQuery = `
      SELECT f.CompanyID, c.CompanyName, COUNT(*) as ConfigCount
      FROM tblLaneFastagConfiguration f
      LEFT JOIN tblCompanyMaster c ON f.CompanyID = c.Id
      GROUP BY f.CompanyID, c.CompanyName
    `;
    const fastagCountResult = await db.query(fastagCountQuery);
    
    // Get all FasTag configurations for this user's companies
    let userFastagQuery = `
      SELECT f.RecordID, f.CompanyID, c.CompanyName, f.PlazaID, p.PlazaName, f.LaneNumber
      FROM tblLaneFastagConfiguration f
      LEFT JOIN tblCompanyMaster c ON f.CompanyID = c.Id
      LEFT JOIN Plaza p ON f.PlazaID = p.Id
    `;
    
    if (user.role === 'CompanyAdmin') {
      userFastagQuery += `
        WHERE f.CompanyID IN (
          SELECT CompanyId FROM UserCompany
          WHERE UserId = @userId AND IsActive = 1
        )
      `;
    }
    
    const userFastagResult = await db.query(userFastagQuery, { userId: user.id });
    
    // Check if there are any issues that need fixing
    let issues = [];
    let fixesApplied = [];
    
    // For CompanyAdmin, check if they have active company associations
    if (user.role === 'CompanyAdmin') {
      const activeCompanyAssociations = userCompanyResult.recordset.filter(c => c.IsActive === true || c.IsActive === 1);
      
      if (activeCompanyAssociations.length === 0) {
        issues.push('CompanyAdmin has no active company associations');
        
        // Try to fix: Activate all inactive associations
        const inactiveAssociations = userCompanyResult.recordset.filter(c => c.IsActive === false || c.IsActive === 0);
        
        if (inactiveAssociations.length > 0) {
          // Activate the inactive associations
          for (const association of inactiveAssociations) {
            await db.query(`
              UPDATE UserCompany
              SET IsActive = 1
              WHERE UserId = @userId AND CompanyId = @companyId
            `, {
              userId: user.id,
              companyId: association.CompanyId
            });
            
            fixesApplied.push(`Activated company association for CompanyId: ${association.CompanyId}`);
          }
        } else {
          // If no associations exist at all, try to find companies with FasTag configurations
          // and create associations for them
          const companiesWithFastag = fastagCountResult.recordset;
          
          if (companiesWithFastag.length > 0) {
            for (const company of companiesWithFastag) {
              await db.query(`
                INSERT INTO UserCompany (UserId, CompanyId, IsActive, CreatedBy, CreatedOn)
                VALUES (@userId, @companyId, 1, 1, GETDATE())
              `, {
                userId: user.id,
                companyId: company.CompanyID
              });
              
              fixesApplied.push(`Created new company association for CompanyId: ${company.CompanyID}`);
            }
          }
        }
      }
    }
    
    return responseHandler.success(res, {
      user: {
        id: user.id,
        username: user.username,
        role: user.role
      },
      companyAssociations: userCompanyResult.recordset,
      fastagCountsByCompany: fastagCountResult.recordset,
      userFastagConfigurations: userFastagResult.recordset,
      issues,
      fixesApplied
    }, 'Diagnostic information retrieved successfully');
  } catch (error) {
    console.error('Error in diagnostic check:', error);
    return responseHandler.error(res, error.message);
  }
};

// Fix user company associations
exports.fixUserCompanyAssociations = async (req, res) => {
  try {
    const user = req.user;
    
    // Only allow this for CompanyAdmin users
    if (user.role !== 'CompanyAdmin' && user.role !== 'SuperAdmin') {
      return responseHandler.forbidden(res, 'Only CompanyAdmin and SuperAdmin users can use this endpoint');
    }
    
    // Get all companies with FasTag configurations
    const companiesWithFastagQuery = `
      SELECT DISTINCT f.CompanyID, c.CompanyName
      FROM tblLaneFastagConfiguration f
      LEFT JOIN tblCompanyMaster c ON f.CompanyID = c.Id
    `;
    const companiesWithFastagResult = await db.query(companiesWithFastagQuery);
    
    // Get user's current company associations
    const userCompanyQuery = `
      SELECT CompanyId, IsActive
      FROM UserCompany
      WHERE UserId = @userId
    `;
    const userCompanyResult = await db.query(userCompanyQuery, { userId: user.id });
    
    // Create a map of existing associations
    const existingAssociations = {};
    userCompanyResult.recordset.forEach(assoc => {
      existingAssociations[assoc.CompanyId] = assoc.IsActive;
    });
    
    const fixesApplied = [];
    
    // For each company with FasTag configurations
    for (const company of companiesWithFastagResult.recordset) {
      const companyId = company.CompanyID;
      
      // If user already has an active association, skip
      if (existingAssociations[companyId] === true || existingAssociations[companyId] === 1) {
        continue;
      }
      
      // If user has an inactive association, activate it
      if (existingAssociations[companyId] === false || existingAssociations[companyId] === 0) {
        await db.query(`
          UPDATE UserCompany
          SET IsActive = 1
          WHERE UserId = @userId AND CompanyId = @companyId
        `, {
          userId: user.id,
          companyId
        });
        
        fixesApplied.push(`Activated company association for CompanyId: ${companyId}`);
      } else {
        // If no association exists, create one
        await db.query(`
          INSERT INTO UserCompany (UserId, CompanyId, IsActive, CreatedBy, CreatedOn)
          VALUES (@userId, @companyId, 1, 1, GETDATE())
        `, {
          userId: user.id,
          companyId
        });
        
        fixesApplied.push(`Created new company association for CompanyId: ${companyId}`);
      }
    }
    
    return responseHandler.success(res, {
      fixesApplied,
      companiesWithFastag: companiesWithFastagResult.recordset.length
    }, 'User company associations fixed successfully');
  } catch (error) {
    console.error('Error fixing user company associations:', error);
    return responseHandler.error(res, error.message);
  }
};

// Get all lane Fastag configurations
exports.getAllLaneFastagConfigurations = async (req, res) => {
  try {
    // Get user information from the request
    const user = req.user;
    console.log('User requesting FasTag configurations:', {
      id: user.id,
      role: user.role,
      username: user.username
    });

    // Base query with joins for related data - only show active configurations
    let baseQuery = `
      SELECT
        f.RecordID, f.PlazaID, f.CompanyID, f.LaneID, f.LaneNumber, f.PlazaGeoCode,
        f.PlazaSubType, f.PlazaType, f.LaneDirection, f.LaneReaderID, f.LaneStatus,
        f.LaneMode, f.LaneType, f.LaneFloor, f.UpdatedDateTime, f.UpdatedBy,
        f.FastagOrgID, f.FastagAgencyCode, f.FastagAPIAddress, f.LaneGate, f.flgTerminal1Exit,
        c.CompanyName,
        p.PlazaName
      FROM tblLaneFastagConfiguration f
      LEFT JOIN tblCompanyMaster c ON f.CompanyID = c.Id
      LEFT JOIN Plaza p ON f.PlazaID = p.Id
      WHERE (f.LaneStatus != 'Inactive' OR f.LaneStatus IS NULL)
    `;

    const queryParams = {};

    // Apply filtering based on user role
    if (user.role === 'SuperAdmin') {
      // SuperAdmin can see all configurations
      // No additional filters needed
      console.log('SuperAdmin: showing all FasTag configurations');
    } else if (user.role === 'CompanyAdmin') {
      // First, check if the user has any company associations
      const userCompanyQuery = `
        SELECT CompanyId FROM UserCompany
        WHERE UserId = @userId AND IsActive = 1
      `;
      const userCompanyResult = await db.query(userCompanyQuery, { userId: user.id });
      
      if (userCompanyResult.recordset.length === 0) {
        console.log(`CompanyAdmin (ID: ${user.id}) has no company associations in UserCompany table`);
        
        // Fallback: Try to get companies from the user object if available
        if (user.companies && Array.isArray(user.companies) && user.companies.length > 0) {
          const companyIds = user.companies.map(company => company.Id).filter(id => id);
          if (companyIds.length > 0) {
            console.log(`Using companies from user object: ${companyIds.join(', ')}`);
            baseQuery += `
              AND f.CompanyID IN (${companyIds.join(', ')})
            `;
          }
        } else {
          console.log('No companies found in user object either');
        }
      } else {
        // CompanyAdmin can only see configurations for their companies
        const companyIds = userCompanyResult.recordset.map(row => row.CompanyId);
        console.log(`CompanyAdmin has access to companies: ${companyIds.join(', ')}`);
        
        baseQuery += `
          AND f.CompanyID IN (
            SELECT CompanyId FROM UserCompany
            WHERE UserId = @userId AND IsActive = 1
          )
        `;
        queryParams.userId = user.id;
      }
    } else if (user.role === 'PlazaManager') {
      // PlazaManager can only see configurations for their plazas
      baseQuery += `
        AND f.PlazaID IN (
          SELECT PlazaId FROM UserPlaza
          WHERE UserId = @userId AND IsActive = 1
        )
      `;
      queryParams.userId = user.id;
      console.log(`PlazaManager (ID: ${user.id}): filtering by assigned plazas`);
    }

    // Add ordering
    baseQuery += ` ORDER BY f.RecordID DESC`;

    console.log('Executing FasTag query with params:', queryParams);
    // Execute the query
    const result = await db.query(baseQuery, queryParams);
    console.log(`Query returned ${result.recordset.length} FasTag configurations`);

    return responseHandler.success(res, result.recordset, 'Fastag configurations retrieved successfully');
  } catch (error) {
    console.error('Error fetching lane Fastag configurations:', error);
    return responseHandler.error(res, error.message);
  }
};

/**
 * Retrieves a specific lane Fastag configuration by its ID
 * @param {number} id - The RecordID of the configuration to retrieve
 * @returns {Object} The lane Fastag configuration data
 */
exports.getLaneFastagConfigurationById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get user information from the request
    const user = req.user;

    // Base query to get the Fastag configuration with related data - only show active configurations
    const query = `
      SELECT
        f.RecordID, f.PlazaID, f.CompanyID, f.LaneID, f.LaneNumber, f.PlazaGeoCode,
        f.PlazaSubType, f.PlazaType, f.LaneDirection, f.LaneReaderID, f.LaneStatus,
        f.LaneMode, f.LaneType, f.LaneFloor, f.UpdatedDateTime, f.UpdatedBy,
        f.FastagOrgID, f.FastagAgencyCode, f.FastagAPIAddress, f.LaneGate, f.flgTerminal1Exit,
        c.CompanyName,
        p.PlazaName
      FROM tblLaneFastagConfiguration f
      LEFT JOIN tblCompanyMaster c ON f.CompanyID = c.Id
      LEFT JOIN Plaza p ON f.PlazaID = p.Id
      WHERE f.RecordID = @RecordID AND (f.LaneStatus != 'Inactive' OR f.LaneStatus IS NULL)
    `;

    const result = await db.query(query, { RecordID: id });

    if (result.recordset.length === 0) {
      return responseHandler.notFound(res, 'Lane Fastag configuration not found');
    }

    const fastagConfig = result.recordset[0];

    // Check if user has access to this configuration based on role
    if (user.role !== 'SuperAdmin') {
      if (user.role === 'CompanyAdmin') {
        // Check if the configuration belongs to one of the user's companies
        const companyAccessQuery = `
          SELECT 1
          FROM UserCompany
          WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
        `;
        const companyAccessResult = await db.query(companyAccessQuery, {
          userId: user.id,
          companyId: fastagConfig.CompanyID
        });

        if (companyAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this Fastag configuration');
        }
      } else if (user.role === 'PlazaManager') {
        // Check if the configuration belongs to one of the user's plazas
        const plazaAccessQuery = `
          SELECT 1
          FROM UserPlaza
          WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
        `;
        const plazaAccessResult = await db.query(plazaAccessQuery, {
          userId: user.id,
          plazaId: fastagConfig.PlazaID
        });

        if (plazaAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this Fastag configuration');
        }
      }
    }

    return responseHandler.success(res, fastagConfig, 'Fastag configuration retrieved successfully');
  } catch (error) {
    console.error('Error fetching lane Fastag configuration:', error);
    return responseHandler.error(res, error.message);
  }
};

/**
 * Retrieves all lane Fastag configurations for a specific plaza
 * @param {number} plazaId - The ID of the plaza to retrieve configurations for
 * @returns {Array} Array of lane Fastag configurations for the specified plaza
 */
exports.getLaneFastagConfigurationsByPlazaId = async (req, res) => {
  try {
    const { plazaId } = req.params;

    // Get user information from the request
    const user = req.user;

    // Check if user has access to this plaza
    if (user.role !== 'SuperAdmin') {
      let hasAccess = false;

      if (user.role === 'CompanyAdmin') {
        // Check if the plaza belongs to one of the user's companies
        const plazaCompanyQuery = `
          SELECT p.CompanyId
          FROM Plaza p
          JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
          WHERE p.Id = @plazaId AND uc.UserId = @userId AND uc.IsActive = 1
        `;
        const plazaCompanyResult = await db.query(plazaCompanyQuery, {
          plazaId,
          userId: user.id
        });

        hasAccess = plazaCompanyResult.recordset.length > 0;
      } else if (user.role === 'PlazaManager') {
        // Check if the user is assigned to this plaza
        const plazaAccessQuery = `
          SELECT 1
          FROM UserPlaza
          WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
        `;
        const plazaAccessResult = await db.query(plazaAccessQuery, {
          userId: user.id,
          plazaId
        });

        hasAccess = plazaAccessResult.recordset.length > 0;
      }

      if (!hasAccess) {
        return responseHandler.forbidden(res, 'You do not have access to this plaza');
      }
    }

    // Query to get all active Fastag configurations for the plaza
    const query = `
      SELECT
        f.RecordID, f.PlazaID, f.CompanyID, f.LaneID, f.LaneNumber, f.PlazaGeoCode,
        f.PlazaSubType, f.PlazaType, f.LaneDirection, f.LaneReaderID, f.LaneStatus,
        f.LaneMode, f.LaneType, f.LaneFloor, f.UpdatedDateTime, f.UpdatedBy,
        f.FastagOrgID, f.FastagAgencyCode, f.FastagAPIAddress, f.LaneGate, f.flgTerminal1Exit,
        c.CompanyName,
        p.PlazaName
      FROM tblLaneFastagConfiguration f
      LEFT JOIN tblCompanyMaster c ON f.CompanyID = c.Id
      LEFT JOIN Plaza p ON f.PlazaID = p.Id
      WHERE f.PlazaID = @PlazaID AND (f.LaneStatus != 'Inactive' OR f.LaneStatus IS NULL)
      ORDER BY f.RecordID DESC
    `;

    const result = await db.query(query, { PlazaID: plazaId });

    return responseHandler.success(res, result.recordset, 'Fastag configurations for plaza retrieved successfully');
  } catch (error) {
    console.error('Error fetching lane Fastag configurations by PlazaID:', error);
    return responseHandler.error(res, error.message);
  }
};

/**
 * ============================================================================
 * # CREATION OPERATIONS
 * ============================================================================
 * Methods for creating new lane Fastag configurations
 */

/**
 * Creates a new lane Fastag configuration
 * @param {Object} req.body - The configuration data to create
 * @returns {Object} Success message and status
 */
exports.createLaneFastagConfiguration = async (req, res) => {
  const {
    PlazaID,
    CompanyID,
    LaneID,
    LaneNumber,
    PlazaGeoCode,
    PlazaName,
    PlazaSubType,
    PlazaType,
    LaneDirection,
    LaneReaderID,
    LaneStatus,
    LaneMode,
    LaneType,
    LaneFloor,
    FastagOrgID,
    FastagAgencyCode,
    FastagAPIAddress,
    LaneGate,
    flgTerminal1Exit
  } = req.body;

  // Basic validation
  if (!PlazaID || !CompanyID || !LaneID) {
    return responseHandler.badRequest(res, 'PlazaID, CompanyID, and LaneID are required');
  }

  try {
    // Get user information from the request
    const user = req.user;

    // Check if user has permission to create Fastag configuration for this company/plaza
    const hasCreatePermission = await hasPermission(
      user,
      'Fastag',
      'Create',
      { companyId: CompanyID, plazaId: PlazaID, laneId: LaneID }
    );

    if (!hasCreatePermission) {
      return responseHandler.forbidden(res, 'You do not have permission to create Fastag configuration for this plaza/lane');
    }

    // Use the authenticated user's ID for UpdatedBy
    const UpdatedBy = user.id;

    await db.query(
      `INSERT INTO tblLaneFastagConfiguration
       (PlazaID, CompanyID, LaneID, LaneNumber, PlazaGeoCode, PlazaName,
        PlazaSubType, PlazaType, LaneDirection, LaneReaderID, LaneStatus,
        LaneMode, LaneType, LaneFloor, UpdatedDateTime, UpdatedBy,
        FastagOrgID, FastagAgencyCode, FastagAPIAddress, LaneGate, flgTerminal1Exit)
       VALUES
       (@PlazaID, @CompanyID, @LaneID, @LaneNumber, @PlazaGeoCode, @PlazaName,
        @PlazaSubType, @PlazaType, @LaneDirection, @LaneReaderID, @LaneStatus,
        @LaneMode, @LaneType, @LaneFloor, GETDATE(), @UpdatedBy,
        @FastagOrgID, @FastagAgencyCode, @FastagAPIAddress, @LaneGate, @flgTerminal1Exit)`,
      {
        PlazaID,
        CompanyID,
        LaneID,
        LaneNumber,
        PlazaGeoCode,
        PlazaName,
        PlazaSubType,
        PlazaType,
        LaneDirection,
        LaneReaderID,
        LaneStatus,
        LaneMode,
        LaneType,
        LaneFloor,
        UpdatedBy,
        FastagOrgID,
        FastagAgencyCode,
        FastagAPIAddress,
        LaneGate,
        flgTerminal1Exit
      }
    );

    return responseHandler.created(res, null, 'Lane Fastag configuration created successfully');
  } catch (error) {
    console.error('Error creating lane Fastag configuration:', error);
    return responseHandler.error(res, error.message);
  }
};

/**
 * ============================================================================
 * # UPDATE OPERATIONS
 * ============================================================================
 * Methods for updating existing lane Fastag configurations
 */

/**
 * Updates an existing lane Fastag configuration
 * @param {number} id - The RecordID of the configuration to update
 * @param {Object} req.body - The updated configuration data
 * @returns {Object} Success message and status
 */
exports.updateLaneFastagConfiguration = async (req, res) => {
  const id = req.params.id;
  const {
    PlazaID,
    CompanyID,
    LaneID,
    LaneNumber,
    PlazaGeoCode,
    PlazaName,
    PlazaSubType,
    PlazaType,
    LaneDirection,
    LaneReaderID,
    LaneStatus,
    LaneMode,
    LaneType,
    LaneFloor,
    FastagOrgID,
    FastagAgencyCode,
    FastagAPIAddress,
    LaneGate,
    flgTerminal1Exit
  } = req.body;

  try {
    // Get user information from the request
    const user = req.user;

    // First, check if the Fastag configuration exists
    const checkQuery = `
      SELECT f.*, p.CompanyId as PlazaCompanyId
      FROM tblLaneFastagConfiguration f
      LEFT JOIN Plaza p ON f.PlazaID = p.Id
      WHERE f.RecordID = @RecordID
    `;

    const checkResult = await db.query(checkQuery, { RecordID: id });

    if (checkResult.recordset.length === 0) {
      return responseHandler.notFound(res, 'Lane Fastag configuration not found');
    }

    const existingConfig = checkResult.recordset[0];

    // Check if user has permission to edit this Fastag configuration
    const hasEditPermission = await hasPermission(
      user,
      'Fastag',
      'Edit',
      { companyId: existingConfig.CompanyID, plazaId: existingConfig.PlazaID, laneId: existingConfig.LaneID }
    );

    if (!hasEditPermission) {
      return responseHandler.forbidden(res, 'You do not have permission to update this Fastag configuration');
    }

    // If updating to a different company/plaza/lane, check if user has permission for the new values
    if ((CompanyID && CompanyID !== existingConfig.CompanyID) ||
        (PlazaID && PlazaID !== existingConfig.PlazaID) ||
        (LaneID && LaneID !== existingConfig.LaneID)) {

      const hasPermissionForNewValues = await hasPermission(
        user,
        'Fastag',
        'Edit',
        {
          companyId: CompanyID || existingConfig.CompanyID,
          plazaId: PlazaID || existingConfig.PlazaID,
          laneId: LaneID || existingConfig.LaneID
        }
      );

      if (!hasPermissionForNewValues) {
        return responseHandler.forbidden(res, 'You do not have permission to update to the specified company/plaza/lane');
      }
    }

    // Use the authenticated user's ID for UpdatedBy
    const UpdatedBy = user.id;

    await db.query(
      `UPDATE tblLaneFastagConfiguration
       SET PlazaID = @PlazaID,
           CompanyID = @CompanyID,
           LaneID = @LaneID,
           LaneNumber = @LaneNumber,
           PlazaGeoCode = @PlazaGeoCode,
           PlazaName = @PlazaName,
           PlazaSubType = @PlazaSubType,
           PlazaType = @PlazaType,
           LaneDirection = @LaneDirection,
           LaneReaderID = @LaneReaderID,
           LaneStatus = @LaneStatus,
           LaneMode = @LaneMode,
           LaneType = @LaneType,
           LaneFloor = @LaneFloor,
           UpdatedDateTime = GETDATE(),
           UpdatedBy = @UpdatedBy,
           FastagOrgID = @FastagOrgID,
           FastagAgencyCode = @FastagAgencyCode,
           FastagAPIAddress = @FastagAPIAddress,
           LaneGate = @LaneGate,
           flgTerminal1Exit = @flgTerminal1Exit
       WHERE RecordID = @RecordID`,
      {
        RecordID: id,
        PlazaID,
        CompanyID,
        LaneID,
        LaneNumber,
        PlazaGeoCode,
        PlazaName,
        PlazaSubType,
        PlazaType,
        LaneDirection,
        LaneReaderID,
        LaneStatus,
        LaneMode,
        LaneType,
        LaneFloor,
        UpdatedBy,
        FastagOrgID,
        FastagAgencyCode,
        FastagAPIAddress,
        LaneGate,
        flgTerminal1Exit
      }
    );

    // Fetch the updated record to return it
    const updatedResult = await db.query(`
      SELECT
        f.RecordID, f.PlazaID, f.CompanyID, f.LaneID, f.LaneNumber, f.PlazaGeoCode,
        f.PlazaSubType, f.PlazaType, f.LaneDirection, f.LaneReaderID, f.LaneStatus,
        f.LaneMode, f.LaneType, f.LaneFloor, f.UpdatedDateTime, f.UpdatedBy,
        f.FastagOrgID, f.FastagAgencyCode, f.FastagAPIAddress, f.LaneGate, f.flgTerminal1Exit,
        c.CompanyName,
        p.PlazaName
      FROM tblLaneFastagConfiguration f
      LEFT JOIN tblCompanyMaster c ON f.CompanyID = c.Id
      LEFT JOIN Plaza p ON f.PlazaID = p.Id
      WHERE f.RecordID = @RecordID
    `, { RecordID: id });

    if (updatedResult.recordset.length === 0) {
      return responseHandler.notFound(res, 'Updated Fastag configuration could not be retrieved');
    }

    return responseHandler.success(res, updatedResult.recordset[0], 'Lane Fastag configuration updated successfully');
  } catch (error) {
    console.error('Error updating lane Fastag configuration:', error);
    return responseHandler.error(res, error.message);
  }
};

/**
 * ============================================================================
 * # DELETE OPERATIONS
 * ============================================================================
 * Methods for removing lane Fastag configurations
 */

/**
 * Deletes a lane Fastag configuration by ID
 * @param {number} id - The RecordID of the configuration to delete
 * @returns {Object} Success message and status
 */
exports.deleteLaneFastagConfiguration = async (req, res) => {
  const id = req.params.id;

  if (!id) {
    return responseHandler.badRequest(res, 'Fastag Configuration ID is required for deletion');
  }

  try {
    // Get user information from the request
    const user = req.user;

    // First, check if the Fastag configuration exists
    const checkQuery = `
      SELECT f.*, p.CompanyId as PlazaCompanyId
      FROM tblLaneFastagConfiguration f
      LEFT JOIN Plaza p ON f.PlazaID = p.Id
      WHERE f.RecordID = @RecordID
    `;

    const checkResult = await db.query(checkQuery, { RecordID: id });

    if (checkResult.recordset.length === 0) {
      return responseHandler.notFound(res, 'Lane Fastag configuration not found');
    }

    const existingConfig = checkResult.recordset[0];

    // Check if user has permission to delete this Fastag configuration
    const hasDeletePermission = await hasPermission(
      user,
      'Fastag',
      'Delete',
      { companyId: existingConfig.CompanyID, plazaId: existingConfig.PlazaID, laneId: existingConfig.LaneID }
    );

    if (!hasDeletePermission) {
      return responseHandler.forbidden(res, 'You do not have permission to delete this Fastag configuration');
    }

    // Deletion logic - using soft delete approach by setting LaneStatus to 'Inactive'
    // This will keep the record in the database but mark it as inactive
    await db.query(`
      UPDATE tblLaneFastagConfiguration 
      SET LaneStatus = 'Inactive', 
          UpdatedDateTime = GETDATE(),
          UpdatedBy = @UpdatedBy
      WHERE RecordID = @RecordID
    `, { 
      RecordID: id,
      UpdatedBy: user.id
    });

    return responseHandler.success(res, null, 'Lane Fastag configuration deleted successfully');
  } catch (error) {
    console.error('Error deleting lane Fastag configuration:', error);
    return responseHandler.error(res, error.message);
  }
};

/**
 * ============================================================================
 * # BULK OPERATIONS
 * ============================================================================
 * Methods for handling multiple lane Fastag configurations at once
 */

/**
 * Bulk creates or updates multiple lane Fastag configurations
 * @param {Array} configurations - Array of configuration objects to create/update
 * @returns {Object} Success message and status
 */
exports.bulkCreateOrUpdateLaneFastagConfigurations = async (req, res) => {
  const { configurations } = req.body;

  if (!configurations || !Array.isArray(configurations) || configurations.length === 0) {
    return responseHandler.badRequest(res, 'Invalid or empty configurations array');
  }

  try {
    // Get user information from the request
    const user = req.user;

    // Check if user has access to all the plazas and companies in the configurations
    if (user.role !== 'SuperAdmin') {
      // Get unique plaza and company IDs from the configurations
      const plazaIds = [...new Set(configurations.map(config => config.PlazaID))];
      const companyIds = [...new Set(configurations.map(config => config.CompanyID))];

      if (user.role === 'CompanyAdmin') {
        // Check if the user has access to all the companies
        const companyAccessQuery = `
          SELECT CompanyId
          FROM UserCompany
          WHERE UserId = @userId AND CompanyId IN (${companyIds.join(',')}) AND IsActive = 1
        `;
        const companyAccessResult = await db.query(companyAccessQuery, { userId: user.id });

        const accessibleCompanyIds = companyAccessResult.recordset.map(row => row.CompanyId);

        if (accessibleCompanyIds.length !== companyIds.length) {
          return responseHandler.forbidden(res, 'You do not have permission to update configurations for some of the specified companies');
        }
      } else if (user.role === 'PlazaManager') {
        // Check if the user has access to all the plazas
        const plazaAccessQuery = `
          SELECT PlazaId
          FROM UserPlaza
          WHERE UserId = @userId AND PlazaId IN (${plazaIds.join(',')}) AND IsActive = 1
        `;
        const plazaAccessResult = await db.query(plazaAccessQuery, { userId: user.id });

        const accessiblePlazaIds = plazaAccessResult.recordset.map(row => row.PlazaId);

        if (accessiblePlazaIds.length !== plazaIds.length) {
          return responseHandler.forbidden(res, 'You do not have permission to update configurations for some of the specified plazas');
        }
      }
    }

    // Use the authenticated user's ID for UpdatedBy
    const UpdatedBy = user.id;

    // Start a SQL transaction to ensure data integrity across multiple operations
    const transaction = new sql.Transaction(await db.pool);
    await transaction.begin();

    try {
      // Process each configuration in the array
      for (const config of configurations) {
        // Check if record exists
        const checkResult = await transaction.request()
          .input('PlazaID', sql.Numeric, config.PlazaID)
          .input('LaneID', sql.Numeric, config.LaneID)
          .query('SELECT RecordID FROM tblLaneFastagConfiguration WHERE PlazaID = @PlazaID AND LaneID = @LaneID');

        // Determine whether to update existing record or insert new one
        if (checkResult.recordset.length > 0) {
          // Record exists - update the existing record
          await transaction.request()
            .input('RecordID', sql.Numeric, checkResult.recordset[0].RecordID)
            .input('PlazaID', sql.Numeric, config.PlazaID)
            .input('CompanyID', sql.Numeric, config.CompanyID)
            .input('LaneID', sql.Numeric, config.LaneID)
            .input('LaneNumber', sql.Char, config.LaneNumber)
            .input('PlazaGeoCode', sql.VarChar, config.PlazaGeoCode)
            .input('PlazaName', sql.VarChar, config.PlazaName)
            .input('PlazaSubType', sql.VarChar, config.PlazaSubType)
            .input('PlazaType', sql.VarChar, config.PlazaType)
            .input('LaneDirection', sql.VarChar, config.LaneDirection)
            .input('LaneReaderID', sql.VarChar, config.LaneReaderID)
            .input('LaneStatus', sql.VarChar, config.LaneStatus)
            .input('LaneMode', sql.VarChar, config.LaneMode)
            .input('LaneType', sql.VarChar, config.LaneType)
            .input('LaneFloor', sql.VarChar, config.LaneFloor)
            .input('UpdatedBy', sql.VarChar, UpdatedBy) // Use authenticated user ID
            .input('FastagOrgID', sql.VarChar, config.FastagOrgID)
            .input('FastagAgencyCode', sql.VarChar, config.FastagAgencyCode)
            .input('FastagAPIAddress', sql.VarChar, config.FastagAPIAddress)
            .input('LaneGate', sql.Char, config.LaneGate)
            .input('flgTerminal1Exit', sql.Char, config.flgTerminal1Exit)
            .query(`
              UPDATE tblLaneFastagConfiguration
              SET CompanyID = @CompanyID,
                  LaneNumber = @LaneNumber,
                  PlazaGeoCode = @PlazaGeoCode,
                  PlazaName = @PlazaName,
                  PlazaSubType = @PlazaSubType,
                  PlazaType = @PlazaType,
                  LaneDirection = @LaneDirection,
                  LaneReaderID = @LaneReaderID,
                  LaneStatus = @LaneStatus,
                  LaneMode = @LaneMode,
                  LaneType = @LaneType,
                  LaneFloor = @LaneFloor,
                  UpdatedDateTime = GETDATE(),
                  UpdatedBy = @UpdatedBy,
                  FastagOrgID = @FastagOrgID,
                  FastagAgencyCode = @FastagAgencyCode,
                  FastagAPIAddress = @FastagAPIAddress,
                  LaneGate = @LaneGate,
                  flgTerminal1Exit = @flgTerminal1Exit
              WHERE RecordID = @RecordID
            `);
        } else {
          // Insert new record
          await transaction.request()
            .input('PlazaID', sql.Numeric, config.PlazaID)
            .input('CompanyID', sql.Numeric, config.CompanyID)
            .input('LaneID', sql.Numeric, config.LaneID)
            .input('LaneNumber', sql.Char, config.LaneNumber)
            .input('PlazaGeoCode', sql.VarChar, config.PlazaGeoCode)
            .input('PlazaName', sql.VarChar, config.PlazaName)
            .input('PlazaSubType', sql.VarChar, config.PlazaSubType)
            .input('PlazaType', sql.VarChar, config.PlazaType)
            .input('LaneDirection', sql.VarChar, config.LaneDirection)
            .input('LaneReaderID', sql.VarChar, config.LaneReaderID)
            .input('LaneStatus', sql.VarChar, config.LaneStatus)
            .input('LaneMode', sql.VarChar, config.LaneMode)
            .input('LaneType', sql.VarChar, config.LaneType)
            .input('LaneFloor', sql.VarChar, config.LaneFloor)
            .input('UpdatedBy', sql.VarChar, UpdatedBy) // Use authenticated user ID
            .input('FastagOrgID', sql.VarChar, config.FastagOrgID)
            .input('FastagAgencyCode', sql.VarChar, config.FastagAgencyCode)
            .input('FastagAPIAddress', sql.VarChar, config.FastagAPIAddress)
            .input('LaneGate', sql.Char, config.LaneGate)
            .input('flgTerminal1Exit', sql.Char, config.flgTerminal1Exit)
            .query(`
              INSERT INTO tblLaneFastagConfiguration
              (PlazaID, CompanyID, LaneID, LaneNumber, PlazaGeoCode, PlazaName,
               PlazaSubType, PlazaType, LaneDirection, LaneReaderID, LaneStatus,
               LaneMode, LaneType, LaneFloor, UpdatedDateTime, UpdatedBy,
               FastagOrgID, FastagAgencyCode, FastagAPIAddress, LaneGate, flgTerminal1Exit)
              VALUES
              (@PlazaID, @CompanyID, @LaneID, @LaneNumber, @PlazaGeoCode, @PlazaName,
               @PlazaSubType, @PlazaType, @LaneDirection, @LaneReaderID, @LaneStatus,
               @LaneMode, @LaneType, @LaneFloor, GETDATE(), @UpdatedBy,
               @FastagOrgID, @FastagAgencyCode, @FastagAPIAddress, @LaneGate, @flgTerminal1Exit)
            `);
        }
      }

      await transaction.commit();

      return responseHandler.success(res, null, 'Bulk lane Fastag configurations created/updated successfully');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error in bulk operation for lane Fastag configurations:', error);
    return responseHandler.error(res, error.message);
  }
};