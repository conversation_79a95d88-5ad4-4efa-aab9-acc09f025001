const db = require('./backend/src/config/database');

async function checkLaneNullability() {
  try {
    console.log('=== CHECKING LANE TABLE NULL CONSTRAINTS ===');
    
    // Check which columns don't allow NULL
    const nullConstraints = await db.query(`
      SELECT 
        COLUMN_NAME, 
        DATA_TYPE, 
        IS_NULLABLE,
        COLUMN_DEFAULT,
        CHARACTER_MAXIMUM_LENGTH
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblLaneDetails'
      AND IS_NULLABLE = 'NO'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\nColumns that DO NOT allow NULL:');
    console.log('=====================================');
    nullConstraints.recordset.forEach(col => {
      console.log(`${col.COLUMN_NAME.padEnd(25)} | ${col.DATA_TYPE.padEnd(12)} | Max: ${col.CHARACTER_MAXIMUM_LENGTH || 'N/A'} | Default: ${col.COLUMN_DEFAULT || 'None'}`);
    });
    
    // Check which columns DO allow NULL
    const allowNullConstraints = await db.query(`
      SELECT 
        COLUMN_NAME, 
        DATA_TYPE, 
        IS_NULLABLE,
        COLUMN_DEFAULT,
        CHARACTER_MAXIMUM_LENGTH
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblLaneDetails'
      AND IS_NULLABLE = 'YES'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n\nColumns that ALLOW NULL:');
    console.log('=====================================');
    allowNullConstraints.recordset.forEach(col => {
      console.log(`${col.COLUMN_NAME.padEnd(25)} | ${col.DATA_TYPE.padEnd(12)} | Max: ${col.CHARACTER_MAXIMUM_LENGTH || 'N/A'} | Default: ${col.COLUMN_DEFAULT || 'None'}`);
    });
    
    // Generate recommendations
    console.log('\n\n=== RECOMMENDATIONS FOR BACKEND FIXES ===');
    console.log('Fields that need non-NULL defaults:');
    
    const nonNullFields = nullConstraints.recordset;
    nonNullFields.forEach(field => {
      if (field.DATA_TYPE === 'numeric') {
        console.log(`✅ ${field.COLUMN_NAME}: Use 0 as default (numeric field)`);
      } else if (field.DATA_TYPE === 'char') {
        if (field.COLUMN_NAME.includes('flg') || field.COLUMN_NAME.includes('Flag') || 
            field.COLUMN_NAME === 'ActiveStatus' || field.COLUMN_NAME === 'DisplayPole' ||
            field.COLUMN_NAME === 'CashDrawer' || field.COLUMN_NAME === 'MultipleExit' ||
            field.COLUMN_NAME === 'Antipassback' || field.COLUMN_NAME === 'HFPasscard' ||
            field.COLUMN_NAME === 'APS_Exit' || field.COLUMN_NAME === 'PayTmPG' ||
            field.COLUMN_NAME === 'FlgLPRCamera' || field.COLUMN_NAME === 'fRecyclerStatus') {
          console.log(`✅ ${field.COLUMN_NAME}: Use 'N' as default (boolean-like char field)`);
        } else {
          console.log(`✅ ${field.COLUMN_NAME}: Use '' (empty string) as default (text field)`);
        }
      } else {
        console.log(`⚠️  ${field.COLUMN_NAME}: Check appropriate default for ${field.DATA_TYPE}`);
      }
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Check failed:', error);
    process.exit(1);
  }
}

checkLaneNullability();