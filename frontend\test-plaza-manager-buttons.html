<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlazaManager Action Buttons Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .module-test {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f9fafb;
            border-radius: 4px;
            border-left: 4px solid #10b981;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        .btn-edit {
            background: #3b82f6;
            color: white;
        }
        .btn-edit:hover {
            background: #2563eb;
        }
        .btn-delete {
            background: #ef4444;
            color: white;
        }
        .btn-delete:hover {
            background: #dc2626;
        }
        .btn-create {
            background: #10b981;
            color: white;
        }
        .btn-create:hover {
            background: #059669;
        }
        .status {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status-visible {
            background: #dcfce7;
            color: #166534;
        }
        .status-redirect {
            background: #fef3c7;
            color: #92400e;
        }
        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            color: #1e40af;
            margin-top: 0;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        .test-result h3 {
            color: #166534;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <h1>🔒 PlazaManager Action Buttons Test</h1>
    
    <div class="instructions">
        <h3>📋 Test Instructions</h3>
        <p><strong>Scenario:</strong> You are logged in as a PlazaManager user</p>
        <p><strong>Expected Behavior:</strong></p>
        <ul>
            <li>✅ All action buttons should be <strong>VISIBLE</strong> across all modules</li>
            <li>🚫 Clicking any button should <strong>REDIRECT</strong> to unauthorized page</li>
            <li>💬 Each redirect should show a <strong>CUSTOM MESSAGE</strong> for the specific module</li>
        </ul>
        <p><strong>Test Method:</strong> Click each button below to verify the redirect behavior</p>
    </div>

    <div class="test-section">
        <h2 class="test-header">🏢 Company Management</h2>
        <div class="module-test">
            <div>
                <strong>Company Actions</strong>
                <br><small>Module: Companies | Permissions: Edit, Delete</small>
            </div>
            <div class="action-buttons">
                <button class="btn btn-edit" onclick="testRedirect('Companies', 'edit')">
                    ✏️ Edit Company
                </button>
                <button class="btn btn-delete" onclick="testRedirect('Companies', 'delete')">
                    🗑️ Delete Company
                </button>
                <span class="status status-visible">VISIBLE</span>
                <span class="status status-redirect">REDIRECTS</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-header">🏪 Plaza Management</h2>
        <div class="module-test">
            <div>
                <strong>Plaza Actions</strong>
                <br><small>Module: Plazas | Permissions: Edit, Delete, Create</small>
            </div>
            <div class="action-buttons">
                <button class="btn btn-create" onclick="testRedirect('Plazas', 'create')">
                    ➕ Create Plaza
                </button>
                <button class="btn btn-edit" onclick="testRedirect('Plazas', 'edit')">
                    ✏️ Edit Plaza
                </button>
                <button class="btn btn-delete" onclick="testRedirect('Plazas', 'delete')">
                    🗑️ Delete Plaza
                </button>
                <span class="status status-visible">VISIBLE</span>
                <span class="status status-redirect">REDIRECTS</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-header">🛣️ Lane Management</h2>
        <div class="module-test">
            <div>
                <strong>Lane Actions</strong>
                <br><small>Module: Lanes | Permissions: Edit, Delete, Create</small>
            </div>
            <div class="action-buttons">
                <button class="btn btn-create" onclick="testRedirect('Lanes', 'create')">
                    ➕ Create Lane
                </button>
                <button class="btn btn-edit" onclick="testRedirect('Lanes', 'edit')">
                    ✏️ Edit Lane
                </button>
                <button class="btn btn-delete" onclick="testRedirect('Lanes', 'delete')">
                    🗑️ Delete Lane
                </button>
                <span class="status status-visible">VISIBLE</span>
                <span class="status status-redirect">REDIRECTS</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-header">📷 ANPR Configuration</h2>
        <div class="module-test">
            <div>
                <strong>ANPR Actions</strong>
                <br><small>Module: ANPR | Permissions: Edit, Delete, Create</small>
            </div>
            <div class="action-buttons">
                <button class="btn btn-create" onclick="testRedirect('ANPR', 'create')">
                    ➕ Create ANPR
                </button>
                <button class="btn btn-edit" onclick="testRedirect('ANPR', 'edit')">
                    ✏️ Edit ANPR
                </button>
                <button class="btn btn-delete" onclick="testRedirect('ANPR', 'delete')">
                    🗑️ Delete ANPR
                </button>
                <span class="status status-visible">VISIBLE</span>
                <span class="status status-redirect">REDIRECTS</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-header">💳 Digital Payment Configuration</h2>
        <div class="module-test">
            <div>
                <strong>Digital Payment Actions</strong>
                <br><small>Module: DigitalPayment | Permissions: Edit, Delete, Create</small>
            </div>
            <div class="action-buttons">
                <button class="btn btn-create" onclick="testRedirect('DigitalPayment', 'create')">
                    ➕ Create Config
                </button>
                <button class="btn btn-edit" onclick="testRedirect('DigitalPayment', 'edit')">
                    ✏️ Edit Config
                </button>
                <button class="btn btn-delete" onclick="testRedirect('DigitalPayment', 'delete')">
                    🗑️ Delete Config
                </button>
                <span class="status status-visible">VISIBLE</span>
                <span class="status status-redirect">REDIRECTS</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-header">🏷️ Fastag Configuration</h2>
        <div class="module-test">
            <div>
                <strong>Fastag Actions</strong>
                <br><small>Module: Fastag | Permissions: Edit, Delete, Create</small>
            </div>
            <div class="action-buttons">
                <button class="btn btn-create" onclick="testRedirect('Fastag', 'create')">
                    ➕ Create Fastag
                </button>
                <button class="btn btn-edit" onclick="testRedirect('Fastag', 'edit')">
                    ✏️ Edit Fastag
                </button>
                <button class="btn btn-delete" onclick="testRedirect('Fastag', 'delete')">
                    🗑️ Delete Fastag
                </button>
                <span class="status status-visible">VISIBLE</span>
                <span class="status status-redirect">REDIRECTS</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-header">👥 User Management</h2>
        <div class="module-test">
            <div>
                <strong>User Actions</strong>
                <br><small>Module: Users | Permissions: Edit, Delete, Create</small>
            </div>
            <div class="action-buttons">
                <button class="btn btn-create" onclick="testRedirect('Users', 'create')">
                    ➕ Create User
                </button>
                <button class="btn btn-edit" onclick="testRedirect('Users', 'edit')">
                    ✏️ Edit User
                </button>
                <button class="btn btn-delete" onclick="testRedirect('Users', 'delete')">
                    🗑️ Delete User
                </button>
                <span class="status status-visible">VISIBLE</span>
                <span class="status status-redirect">REDIRECTS</span>
            </div>
        </div>
    </div>

    <div class="test-result">
        <h3>✅ Expected Test Results</h3>
        <p><strong>For each button click, you should see:</strong></p>
        <ul>
            <li>🚫 Redirect to <code>/unauthorized</code> page</li>
            <li>💬 Custom message: "Plaza Managers can view [module] but cannot [action] them"</li>
            <li>🔒 No actual create/edit/delete operation performed</li>
        </ul>
        <p><strong>Example Messages:</strong></p>
        <ul>
            <li>"Plaza Managers can view companies but cannot edit them"</li>
            <li>"Plaza Managers can view plazas but cannot delete them"</li>
            <li>"Plaza Managers can view users but cannot create them"</li>
        </ul>
    </div>

    <script>
        function testRedirect(module, action) {
            // Simulate the redirect behavior
            const moduleName = module.toLowerCase().replace(/([A-Z])/g, ' $1').trim();
            const message = `Plaza Managers can view ${moduleName} but cannot ${action} them`;
            
            // In real app, this would be: navigate('/unauthorized', { state: { message } });
            alert(`🚫 REDIRECT TO UNAUTHORIZED PAGE\n\nMessage: "${message}"\n\nIn the real app, this would redirect to /unauthorized page with this message.`);
        }
    </script>
</body>
</html>
